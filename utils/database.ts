import * as SQLite from 'expo-sqlite';

export interface JournalEntry {
  id: number;
  type: 'gratitude' | 'learned' | 'accomplishment' | 'delight';
  content: string;
  tags?: string;
  photo_path?: string;
  date: string;
  timestamp: string;
}

export interface Habit {
  id: number;
  name: string;
  schedule: string;
  reminder_time: string;
  streak: number;
  longest_streak: number;
  created_at: string;
  notification_ids?: string;
}

export interface HabitCompletion {
  id: number;
  habit_id: number;
  date: string;
  completed: boolean;
  timestamp: string;
}

export interface BreathingSession {
  id: number;
  duration: number;
  pattern: string;
  completed: boolean;
  date: string;
  timestamp: string;
}

export interface DistractionEntry {
  id: number;
  what: string;
  when_occurred: string;
  why: string;
  date: string;
  timestamp: string;
}

export interface Affirmation {
  id: number;
  text: string;
  is_custom: boolean;
  times_used: number;
  created_at: string;
}

export interface EmotionReflection {
  id: number;
  primary_emotion: string;
  secondary_emotion?: string;
  reflection: string;
  date: string;
  timestamp: string;
}

export interface MoodLog {
  id: number;
  date: string;
  pleasantness: string;
  intensity: string;
  main_contributor: string;
  thoughts_reactions: string;
  frequency: string;
  possible_feelings: string;
  reasons: string;
  created_at: string;
}

export class DatabaseManager {
  private static instance: DatabaseManager;
  private db: SQLite.SQLiteDatabase | null = null;

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  async initDatabase(): Promise<SQLite.SQLiteDatabase> {
    if (this.db) return this.db;

    try {
      this.db = await SQLite.openDatabaseAsync('safespace.db');
      await this.createTables();
      await this.seedDefaultAffirmations();
      return this.db;
    } catch (error) {
      console.error('Database initialization error:', error);
      throw error;
    }
  }

  private async createTables() {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS journal_entries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        content TEXT NOT NULL,
        tags TEXT,
        photo_path TEXT,
        date TEXT NOT NULL,
        timestamp TEXT NOT NULL
      );

      CREATE TABLE IF NOT EXISTS habits (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        schedule TEXT NOT NULL,
        reminder_time TEXT NOT NULL,
        streak INTEGER DEFAULT 0,
        longest_streak INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        notification_ids TEXT
      );

      CREATE TABLE IF NOT EXISTS habit_completions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        habit_id INTEGER NOT NULL,
        date TEXT NOT NULL,
        completed BOOLEAN NOT NULL,
        timestamp TEXT NOT NULL,
        FOREIGN KEY (habit_id) REFERENCES habits (id) ON DELETE CASCADE,
        UNIQUE(habit_id, date)
      );

      CREATE TABLE IF NOT EXISTS breathing_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        duration INTEGER NOT NULL,
        pattern TEXT NOT NULL,
        completed BOOLEAN NOT NULL,
        date TEXT NOT NULL,
        timestamp TEXT NOT NULL
      );

      CREATE TABLE IF NOT EXISTS distraction_entries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        what TEXT NOT NULL,
        when_occurred TEXT NOT NULL,
        why TEXT,
        date TEXT NOT NULL,
        timestamp TEXT NOT NULL
      );

      CREATE TABLE IF NOT EXISTS affirmations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        text TEXT NOT NULL UNIQUE,
        is_custom BOOLEAN NOT NULL,
        times_used INTEGER DEFAULT 0,
        created_at TEXT NOT NULL
      );

      CREATE TABLE IF NOT EXISTS emotion_reflections (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        primary_emotion TEXT NOT NULL,
        secondary_emotion TEXT,
        reflection TEXT,
        date TEXT NOT NULL,
        timestamp TEXT NOT NULL
      );

      CREATE TABLE IF NOT EXISTS mood_entries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        mood INTEGER NOT NULL,
        emotion TEXT,
        notes TEXT,
        date TEXT NOT NULL,
        timestamp TEXT NOT NULL
      );

      CREATE TABLE IF NOT EXISTS mood_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT NOT NULL,
        pleasantness TEXT,
        intensity TEXT,
        main_contributor TEXT,
        thoughts_reactions TEXT,
        frequency TEXT,
        possible_feelings TEXT,
        reasons TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Add notification_ids column to existing habits table if it doesn't exist
    try {
      await this.db.execAsync(`
        ALTER TABLE habits ADD COLUMN notification_ids TEXT;
      `);
    } catch (error) {
      // Column already exists, ignore error
    }
  }

  private async seedDefaultAffirmations() {
    if (!this.db) return;

    const defaultAffirmations = [
      "I am worthy of love and respect",
      "I can handle whatever comes my way",
      "This feeling is temporary and will pass",
      "I am stronger than I think",
      "I deserve peace and happiness",
      "I am doing my best, and that's enough",
      "I choose to focus on what I can control",
      "I am grateful for this moment"
    ];

    for (const text of defaultAffirmations) {
      try {
        await this.db.runAsync(
          'INSERT OR IGNORE INTO affirmations (text, is_custom, created_at) VALUES (?, ?, ?)',
          [text, false, new Date().toISOString()]
        );
      } catch (error) {
        // Ignore duplicate entries
      }
    }
  }

  getDatabase(): SQLite.SQLiteDatabase {
    if (!this.db) throw new Error('Database not initialized');
    return this.db;
  }
}