import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export class NotificationService {
  static async requestPermissions(): Promise<boolean> {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('habit-reminders', {
          name: 'Habit Reminders',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }
      
      return finalStatus === 'granted';
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }

  static async scheduleHabitReminders(
    habitId: number,
    habitName: string,
    reminderTimes: string[],
    selectedDays: string[]
  ): Promise<string[]> {
    try {
      const notificationIds: string[] = [];
      
      // Map day names to weekday numbers (1 = Monday, 7 = Sunday)
      const dayMap: { [key: string]: number } = {
        'Mon': 1, 'Tue': 2, 'Wed': 3, 'Thu': 4, 
        'Fri': 5, 'Sat': 6, 'Sun': 7
      };

      for (const time of reminderTimes) {
        const [hours, minutes] = time.split(':').map(Number);
        
        for (const day of selectedDays) {
          const weekday = dayMap[day];
          if (!weekday) continue;

          const notificationId = await Notifications.scheduleNotificationAsync({
            content: {
              title: 'Habit Reminder',
              body: `Time for your habit: ${habitName}`,
              data: { 
                habitId,
                habitName,
                type: 'habit-reminder'
              },
            },
            trigger: {
              weekday,
              hour: hours,
              minute: minutes,
              repeats: true,
            },
          });

          notificationIds.push(notificationId);
        }
      }

      return notificationIds;
    } catch (error) {
      console.error('Error scheduling habit reminders:', error);
      return [];
    }
  }

  static async cancelHabitReminders(notificationIds: string[]): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationsAsync(notificationIds);
    } catch (error) {
      console.error('Error canceling habit reminders:', error);
    }
  }

  static async cancelAllHabitReminders(): Promise<void> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      const habitNotificationIds = scheduledNotifications
        .filter(notification => notification.content.data?.type === 'habit-reminder')
        .map(notification => notification.identifier);
      
      if (habitNotificationIds.length > 0) {
        await Notifications.cancelScheduledNotificationsAsync(habitNotificationIds);
      }
    } catch (error) {
      console.error('Error canceling all habit reminders:', error);
    }
  }

  static async getScheduledHabitNotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
      return scheduledNotifications.filter(
        notification => notification.content.data?.type === 'habit-reminder'
      );
    } catch (error) {
      console.error('Error getting scheduled notifications:', error);
      return [];
    }
  }
}