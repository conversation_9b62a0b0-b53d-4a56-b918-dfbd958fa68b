/**
 * Consistent spacing system based on multiples of 8
 * This ensures visual consistency throughout the app
 */

export const Spacing = {
  xs: 4,    // 0.5 * base
  sm: 8,    // 1 * base
  md: 16,   // 2 * base
  lg: 24,   // 3 * base
  xl: 32,   // 4 * base
  xxl: 40,  // 5 * base
  xxxl: 48, // 6 * base
} as const;

export type SpacingKey = keyof typeof Spacing;

// Helper function to get spacing value
export const getSpacing = (key: SpacingKey): number => Spacing[key];

// Common spacing combinations
export const SpacingCombinations = {
  // Padding combinations
  paddingHorizontal: {
    sm: { paddingHorizontal: Spacing.sm },
    md: { paddingHorizontal: Spacing.md },
    lg: { paddingHorizontal: Spacing.lg },
    xl: { paddingHorizontal: Spacing.xl },
  },
  paddingVertical: {
    sm: { paddingVertical: Spacing.sm },
    md: { paddingVertical: Spacing.md },
    lg: { paddingVertical: Spacing.lg },
    xl: { paddingVertical: Spacing.xl },
  },
  // Margin combinations
  marginHorizontal: {
    sm: { marginHorizontal: Spacing.sm },
    md: { marginHorizontal: Spacing.md },
    lg: { marginHorizontal: Spacing.lg },
    xl: { marginHorizontal: Spacing.xl },
  },
  marginVertical: {
    sm: { marginVertical: Spacing.sm },
    md: { marginVertical: Spacing.md },
    lg: { marginVertical: Spacing.lg },
    xl: { marginVertical: Spacing.xl },
  },
} as const;