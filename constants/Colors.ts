/**
 * Tranquil Growth Color Palette
 * A calming, trustworthy, and gently encouraging color scheme for mental health and wellness.
 * All colors are WCAG AA compliant for accessibility.
 */

// Tranquil Growth Palette - Light Mode
const primaryColorLight = '#7A936B'; // Muted Sage Green
const backgroundColorLight = '#FAF9F6'; // Warm Off-White
const accentColorLight = '#E07A5F'; // Soft Coral/Terracotta
const textColorLight = '#3C414D'; // Dark Charcoal Grey
const secondaryColorLight = '#AEB7B3'; // Light Grey for secondary elements

// Tranquil Growth Palette - Dark Mode
const primaryColorDark = '#8FA583'; // Lighter Sage Green for dark mode
const backgroundColorDark = '#2A2D32'; // Dark background with slight warmth
const accentColorDark = '#E8927B'; // Lighter Terracotta for dark mode
const textColorDark = '#F0F2F5'; // Light Grey for text in dark mode
const secondaryColorDark = '#6A7173'; // Muted Grey for secondary elements in dark mode

export const Colors = {
  light: {
    text: textColorLight, // Dark Charcoal Grey
    background: backgroundColorLight, // Warm Off-White
    tint: primaryColorLight, // Muted Sage Green
    icon: textColorLight, // Dark Charcoal Grey for icons
    tabIconDefault: secondaryColorLight, // Light Grey for inactive tabs
    tabIconSelected: primaryColorLight, // Muted Sage Green for selected tabs
    accent: accentColorLight, // Soft Coral/Terracotta
    secondary: '#F3F4F6', // Very light gray
    warning: '#F59E0B', // Amber for warnings
    success: primaryColorLight, // Sage Green for success
    error: accentColorLight, // Terracotta for errors
    border: '#E5E7EB', // Light border
    card: backgroundColorLight, // Warm Off-White for cards
    muted: secondaryColorLight, // Light Grey for muted text
  },
  dark: {
    text: textColorDark, // Light Grey for text
    background: backgroundColorDark, // Dark background with slight warmth
    tint: primaryColorDark, // Lighter Sage Green
    icon: textColorDark, // Light Grey for icons
    tabIconDefault: secondaryColorDark, // Muted Grey for inactive tabs
    tabIconSelected: primaryColorDark, // Lighter Sage Green for selected tabs
    accent: accentColorDark, // Lighter Terracotta
    secondary: '#374151', // Dark gray for secondary elements
    warning: '#FBBF24', // Brighter amber for dark mode
    success: primaryColorDark, // Lighter Sage Green for success
    error: accentColorDark, // Lighter Terracotta for errors
    border: '#4B5563', // Dark border
    card: '#374151', // Slightly lighter than background for cards
    muted: secondaryColorDark, // Muted Grey for muted text
  },
};
