# Mental Wellness App with "Tranquil Growth" UI

This application has been refactored with a new "Tranquil Growth" color palette designed specifically for mental health and wellness applications. The new design creates a calming, trustworthy, and gently encouraging user experience.

## Tranquil Growth Color Palette

The application now uses a carefully selected color palette:

- **Primary Color**: `#7A936B` (Muted Sage Green) - Represents growth, balance, and stability
- **Background Color**: `#FAF9F6` (Warm Off-White) - Creates a clean, welcoming space
- **Accent Color**: `#E07A5F` (Soft Coral/Terracotta) - Adds warmth and encourages positive action
- **Text Color**: `#3C414D` (Dark Charcoal Grey) - Ensures high readability and professionalism
- **Secondary Color**: `#AEB7B3` (Light Grey) - Used for secondary elements and inactive states

## Color Roles and Usage

The colors have been applied consistently throughout the application according to these roles:

### Main Backgrounds
- Warm Off-White (`#FAF9F6`) for main screen backgrounds and content containers
- Creates a clean, welcoming space that feels open and calm

### Text and Standard Icons
- Dark Charcoal Grey (`#3C414D`) for all body text, labels, and standard icons
- Ensures high readability while maintaining a professional feel

### Primary Branding and Headers
- Muted Sage Green (`#7A936B`) for main headers, navigation bars, and key brand elements
- Establishes the app's core identity of health, balance, and growth

### Primary Buttons and CTAs
- Soft Coral/Terracotta (`#E07A5F`) for primary buttons and calls-to-action
- Warm color draws attention and encourages positive action
- Text on these buttons uses Warm Off-White (`#FAF9F6`) for strong contrast

### Secondary Buttons and Inactive Elements
- Light Grey (`#AEB7B3`) for secondary actions and disabled elements
- Creates a clear visual hierarchy between primary and secondary actions

### Progress Indicators and Charts
- Muted Sage Green (`#7A936B`) for the base of progress indicators
- Soft Coral/Terracotta (`#E07A5F`) for progress fills and key data points
- Provides rewarding visual cues for achievement

## Accessibility

All color combinations have been verified to meet WCAG AA accessibility standards for contrast ratios, ensuring the app is usable for everyone.

## Components Updated

The following components have been updated with the new color palette:

- Button component (primary, secondary, and outline variants)
- Card component (with softer shadows for a calming effect)
- Typography system (with appropriate colors for different text roles)
- Icons (using the appropriate semantic colors)
- Dashboard and statistics
- Onboarding flow
- Empty states and illustrations

## Dark Mode Support

The application maintains full dark mode support with appropriate color adjustments:

- **Primary Color**: `#8FA583` (Lighter Sage Green)
- **Background Color**: `#2A2D32` (Dark background with slight warmth)
- **Accent Color**: `#E8927B` (Lighter Terracotta)
- **Text Color**: `#F0F2F5` (Light Grey)
- **Secondary Color**: `#6A7173` (Muted Grey)

All dark mode colors maintain the same semantic meaning while ensuring proper contrast and readability in low-light environments.

## Original App Information

This is an [Expo](https://expo.dev) project created with [`create-expo-app`](https://www.npmjs.com/package/create-expo-app).

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Start the app

   ```bash
   npx expo start
   ```

In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).

## Get a fresh project

When you're ready, run:

```bash
npm run reset-project
```

This command will move the starter code to the **app-example** directory and create a blank **app** directory where you can start developing.

## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.

## Join the community

Join our community of developers creating universal apps.

- [Expo on GitHub](https://github.com/expo/expo): View our open source platform and contribute.
- [Discord community](https://chat.expo.dev): Chat with Expo users and ask questions.
