import { useEffect, useState } from 'react';
import { <PERSON>ert, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { DatabaseManager } from '@/utils/database';

interface JournalEntry {
  id: number;
  type: 'gratitude' | 'learned' | 'accomplishment' | 'delight';
  content: string;
  date: string;
  timestamp: string;
}

export default function JournalHistoryScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = useThemeColor({}, 'tint');
  const textColor = useThemeColor({}, 'text');
  const cardColor = useThemeColor({}, 'card');
  const secondaryColor = useThemeColor({}, 'secondary');
  const borderColor = useThemeColor({}, 'border');
  
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'gratitude' | 'learned' | 'accomplishment' | 'delight'>('all');
  const [dbManager] = useState(() => DatabaseManager.getInstance());

  useEffect(() => {
    loadEntries();
  }, [selectedFilter]);

  const loadEntries = async () => {
    try {
      const db = dbManager.getDatabase();
      let query = 'SELECT * FROM journal_entries ORDER BY timestamp DESC';
      let params: any[] = [];

      if (selectedFilter !== 'all') {
        query = 'SELECT * FROM journal_entries WHERE type = ? ORDER BY timestamp DESC';
        params = [selectedFilter];
      }

      const result = await db.getAllAsync(query, params);
      setEntries(result as JournalEntry[]);
    } catch (error) {
      console.error('Error loading journal entries:', error);
      Alert.alert('Error', 'Failed to load journal entries');
    }
  };

  const deleteEntry = async (id: number) => {
    Alert.alert(
      'Delete Entry',
      'Are you sure you want to delete this journal entry?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const db = dbManager.getDatabase();
              await db.runAsync('DELETE FROM journal_entries WHERE id = ?', [id]);
              loadEntries();
            } catch (error) {
              console.error('Error deleting entry:', error);
              Alert.alert('Error', 'Failed to delete entry');
            }
          },
        },
      ]
    );
  };

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
    });
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'gratitude':
        return 'heart.fill';
      case 'learned':
        return 'lightbulb.fill';
      case 'accomplishment':
        return 'star.fill';
      case 'delight':
        return 'face.smiling.fill';
      default:
        return 'doc.text.fill';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'gratitude':
        return '#E74C3C';
      case 'learned':
        return '#F39C12';
      case 'accomplishment':
        return '#27AE60';
      case 'delight':
        return '#9B59B6';
      default:
        return tintColor;
    }
  };

  const renderContent = (entry: JournalEntry) => {
    if (entry.type === 'gratitude') {
      try {
        const gratitudeList = JSON.parse(entry.content);
        return (
          <View>
            {gratitudeList.map((item: string, index: number) => (
              <ThemedText key={index} style={styles.gratitudeItem}>
                • {item}
              </ThemedText>
            ))}
          </View>
        );
      } catch {
        return <ThemedText style={styles.entryContent}>{entry.content}</ThemedText>;
      }
    }
    return <ThemedText style={styles.entryContent}>{entry.content}</ThemedText>;
  };

  const filterButtons = [
    { key: 'all', label: 'All' },
    { key: 'gratitude', label: 'Gratitude' },
    { key: 'learned', label: 'Learned' },
    { key: 'accomplishment', label: 'Goals' },
    { key: 'delight', label: 'Delight' },
  ];

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      <ThemedView style={styles.header}>
        <ThemedView style={styles.headerTop}>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: cardColor, borderColor: borderColor }]}
            onPress={() => router.back()}
          >
            <IconSymbol size={20} name="chevron.left" color={tintColor} />
          </TouchableOpacity>
          <ThemedView style={styles.headerText}>
            <ThemedText type="title">Journal History</ThemedText>
            <ThemedText style={styles.subtitle}>{entries.length} entries</ThemedText>
          </ThemedView>
        </ThemedView>
      </ThemedView>

      {/* Filter Tabs */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.filterContainer}
        contentContainerStyle={styles.filterContent}
      >
        {filterButtons.map((filter) => (
          <TouchableOpacity
            key={filter.key}
            style={[
              styles.filterButton,
              { backgroundColor: selectedFilter === filter.key ? tintColor : secondaryColor }
            ]}
            onPress={() => setSelectedFilter(filter.key as any)}
          >
            <ThemedText style={[
              styles.filterText,
              { color: selectedFilter === filter.key ? 'white' : textColor }
            ]}>
              {filter.label}
            </ThemedText>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Entries List */}
      <ThemedView style={styles.entriesContainer}>
        {entries.length === 0 ? (
          <ThemedView style={[styles.emptyState, { backgroundColor: cardColor }]}>
            <IconSymbol size={48} name="doc.text" color={tintColor} />
            <ThemedText style={styles.emptyTitle}>No entries found</ThemedText>
            <ThemedText style={styles.emptySubtitle}>
              {selectedFilter === 'all' 
                ? 'Start journaling to see your entries here'
                : `No ${selectedFilter} entries yet`
              }
            </ThemedText>
          </ThemedView>
        ) : (
          entries.map((entry) => (
            <ThemedView key={entry.id} style={[styles.entryCard, { backgroundColor: cardColor, borderColor }]}>
              <ThemedView style={styles.entryHeader}>
                <ThemedView style={styles.entryTypeContainer}>
                  <IconSymbol 
                    size={20} 
                    name={getTypeIcon(entry.type)} 
                    color={getTypeColor(entry.type)} 
                  />
                  <ThemedText style={[styles.entryType, { color: getTypeColor(entry.type) }]}>
                    {entry.type.charAt(0).toUpperCase() + entry.type.slice(1)}
                  </ThemedText>
                </ThemedView>
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={() => deleteEntry(entry.id)}
                >
                  <IconSymbol size={16} name="trash" color="#E74C3C" />
                </TouchableOpacity>
              </ThemedView>
              
              <ThemedView style={styles.entryBody}>
                {renderContent(entry)}
              </ThemedView>
              
              <ThemedView style={styles.entryFooter}>
                <ThemedText style={styles.entryDate}>{formatDate(entry.timestamp)}</ThemedText>
                <ThemedText style={styles.entryTime}>{formatTime(entry.timestamp)}</ThemedText>
              </ThemedView>
            </ThemedView>
          ))
        )}
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
    backgroundColor: 'transparent',
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  headerText: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
    marginTop: 4,
  },
  filterContainer: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  filterContent: {
    paddingRight: 20,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
  },
  entriesContainer: {
    padding: 20,
    paddingTop: 0,
    backgroundColor: 'transparent',
  },
  emptyState: {
    padding: 40,
    borderRadius: 16,
    alignItems: 'center',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
  },
  entryCard: {
    borderRadius: 16,
    borderWidth: 1,
    padding: 16,
    marginBottom: 16,
  },
  entryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    backgroundColor: 'transparent',
  },
  entryTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  entryType: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  deleteButton: {
    padding: 8,
  },
  entryBody: {
    marginBottom: 12,
    backgroundColor: 'transparent',
  },
  entryContent: {
    fontSize: 16,
    lineHeight: 24,
  },
  gratitudeItem: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 4,
  },
  entryFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  entryDate: {
    fontSize: 14,
    opacity: 0.7,
  },
  entryTime: {
    fontSize: 14,
    opacity: 0.7,
  },
});