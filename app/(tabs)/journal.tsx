import { useEffect, useState } from 'react';
import { Alert, ScrollView, StyleSheet, TextInput, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { DatabaseManager } from '@/utils/database';

export default function JournalScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = useThemeColor({}, 'tint');
  const textColor = useThemeColor({}, 'text');
  const cardColor = useThemeColor({}, 'card');
  const secondaryColor = useThemeColor({}, 'secondary');
  
  const [activeTab, setActiveTab] = useState<'gratitude' | 'learned' | 'accomplishment' | 'delight'>('gratitude');
  const [gratitudeEntries, setGratitudeEntries] = useState(['', '', '']);
  const [learnedEntries, setLearnedEntries] = useState(['', '', '']);
  const [accomplishmentEntries, setAccomplishmentEntries] = useState(['', '', '']);
  const [delightEntries, setDelightEntries] = useState(['', '', '']);
  const [dbManager] = useState(() => DatabaseManager.getInstance());

  useEffect(() => {
    initDatabase();
  }, []);

  const initDatabase = async () => {
    try {
      await dbManager.initDatabase();
    } catch (error) {
      console.error('Failed to initialize database:', error);
      Alert.alert('Database Error', 'Failed to initialize database');
    }
  };

  const handleGratitudeChange = (index: number, text: string) => {
    const newEntries = [...gratitudeEntries];
    newEntries[index] = text;
    setGratitudeEntries(newEntries);
  };

  const handleLearnedChange = (index: number, text: string) => {
    const newEntries = [...learnedEntries];
    newEntries[index] = text;
    setLearnedEntries(newEntries);
  };

  const handleAccomplishmentChange = (index: number, text: string) => {
    const newEntries = [...accomplishmentEntries];
    newEntries[index] = text;
    setAccomplishmentEntries(newEntries);
  };

  const handleDelightChange = (index: number, text: string) => {
    const newEntries = [...delightEntries];
    newEntries[index] = text;
    setDelightEntries(newEntries);
  };

  const showHistory = () => {
    router.push('/journal-history');
  };

  const saveEntry = async () => {
    try {
      const db = dbManager.getDatabase();
      const now = new Date();
      const date = now.toISOString().split('T')[0];
      const timestamp = now.toISOString();

      let content = '';
      let isEmpty = false;

      switch (activeTab) {
        case 'gratitude':
          const filledGratitudeEntries = gratitudeEntries.filter(entry => entry.trim() !== '');
          if (filledGratitudeEntries.length === 0) {
            isEmpty = true;
          } else {
            content = JSON.stringify(filledGratitudeEntries);
          }
          break;
        case 'learned':
          const filledLearnedEntries = learnedEntries.filter(entry => entry.trim() !== '');
          if (filledLearnedEntries.length === 0) {
            isEmpty = true;
          } else {
            content = JSON.stringify(filledLearnedEntries);
          }
          break;
        case 'accomplishment':
          const filledAccomplishmentEntries = accomplishmentEntries.filter(entry => entry.trim() !== '');
          if (filledAccomplishmentEntries.length === 0) {
            isEmpty = true;
          } else {
            content = JSON.stringify(filledAccomplishmentEntries);
          }
          break;
        case 'delight':
          const filledDelightEntries = delightEntries.filter(entry => entry.trim() !== '');
          if (filledDelightEntries.length === 0) {
            isEmpty = true;
          } else {
            content = JSON.stringify(filledDelightEntries);
          }
          break;
      }

      if (isEmpty) {
        Alert.alert('Empty Entry', 'Please write something before saving.');
        return;
      }

      await db.runAsync(
        'INSERT INTO journal_entries (type, content, date, timestamp) VALUES (?, ?, ?, ?)',
        [activeTab, content, date, timestamp]
      );

      Alert.alert('Saved!', `Your ${activeTab} entry has been saved.`);
      
      // Clear the current tab's content
      switch (activeTab) {
        case 'gratitude':
          setGratitudeEntries(['', '', '']);
          break;
        case 'learned':
          setLearnedEntries(['', '', '']);
          break;
        case 'accomplishment':
          setAccomplishmentEntries(['', '', '']);
          break;
        case 'delight':
          setDelightEntries(['', '', '']);
          break;
      }
    } catch (error) {
      console.error('Error saving journal entry:', error);
      Alert.alert('Save Error', 'Failed to save journal entry');
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      <ThemedView style={styles.header}>
        <ThemedView style={styles.headerTop}>
          <ThemedView style={styles.headerText}>
            <ThemedText type="title">Journal</ThemedText>
            <ThemedText style={styles.subtitle}>Reflect on your day</ThemedText>
          </ThemedView>
          <TouchableOpacity
            style={[styles.historyButton, { backgroundColor: cardColor, borderColor: tintColor }]}
            onPress={showHistory}
          >
            <IconSymbol size={20} name="clock.fill" color={tintColor} />
          </TouchableOpacity>
        </ThemedView>
      </ThemedView>

      {/* Tab Selector */}
      <ThemedView style={[styles.tabContainer, { backgroundColor: secondaryColor }]}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'gratitude' && { backgroundColor: tintColor }
          ]}
          onPress={() => setActiveTab('gratitude')}
        >
          <ThemedText style={[
            styles.tabText,
            activeTab === 'gratitude' && { color: 'white' }
          ]}>
            Gratitude
          </ThemedText>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'learned' && { backgroundColor: tintColor }
          ]}
          onPress={() => setActiveTab('learned')}
        >
          <ThemedText style={[
            styles.tabText,
            activeTab === 'learned' && { color: 'white' }
          ]}>
            Learned
          </ThemedText>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'accomplishment' && { backgroundColor: tintColor }
          ]}
          onPress={() => setActiveTab('accomplishment')}
        >
          <ThemedText style={[
            styles.tabText,
            activeTab === 'accomplishment' && { color: 'white' }
          ]}>
            Goals
          </ThemedText>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'delight' && { backgroundColor: tintColor }
          ]}
          onPress={() => setActiveTab('delight')}
        >
          <ThemedText style={[
            styles.tabText,
            activeTab === 'delight' && { color: 'white' }
          ]}>
            Delight
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>

      {activeTab === 'gratitude' && (
        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>What are you grateful for today?</ThemedText>
          {gratitudeEntries.map((entry, index) => (
            <TextInput
              key={index}
              style={[styles.input, { color: textColor, borderColor: tintColor, backgroundColor: cardColor }]}
              placeholder={`Gratitude ${index + 1}...`}
              placeholderTextColor={textColor + '80'}
              value={entry}
              onChangeText={(text) => handleGratitudeChange(index, text)}
            />
          ))}
        </ThemedView>
      )}

      {activeTab === 'learned' && (
        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>What did you learn today?</ThemedText>
          {learnedEntries.map((entry, index) => (
            <TextInput
              key={index}
              style={[styles.input, { color: textColor, borderColor: tintColor, backgroundColor: cardColor }]}
              placeholder={`Learning ${index + 1}...`}
              placeholderTextColor={textColor + '80'}
              value={entry}
              onChangeText={(text) => handleLearnedChange(index, text)}
            />
          ))}
        </ThemedView>
      )}

      {activeTab === 'accomplishment' && (
        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>What did you accomplish today?</ThemedText>
          {accomplishmentEntries.map((entry, index) => (
            <TextInput
              key={index}
              style={[styles.input, { color: textColor, borderColor: tintColor, backgroundColor: cardColor }]}
              placeholder={`Goal ${index + 1}...`}
              placeholderTextColor={textColor + '80'}
              value={entry}
              onChangeText={(text) => handleAccomplishmentChange(index, text)}
            />
          ))}
        </ThemedView>
      )}

      {activeTab === 'delight' && (
        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>What brought you delight today?</ThemedText>
          {delightEntries.map((entry, index) => (
            <TextInput
              key={index}
              style={[styles.input, { color: textColor, borderColor: tintColor, backgroundColor: cardColor }]}
              placeholder={`Delight ${index + 1}...`}
              placeholderTextColor={textColor + '80'}
              value={entry}
              onChangeText={(text) => handleDelightChange(index, text)}
            />
          ))}
        </ThemedView>
      )}

      <TouchableOpacity
        style={[styles.saveButton, { backgroundColor: tintColor }]}
        onPress={saveEntry}
      >
        <ThemedText style={styles.saveButtonText}>Save Entry</ThemedText>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
    backgroundColor: 'transparent',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  headerText: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
    marginTop: 4,
  },
  historyButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 16,
  },
  tabContainer: {
    flexDirection: 'row',
    margin: 20,
    borderRadius: 12,
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 8,
    alignItems: 'center',
    borderRadius: 8,
  },
  tabText: {
    fontSize: 11,
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: 14,
  },
  section: {
    padding: 20,
    backgroundColor: 'transparent',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  input: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    marginBottom: 12,
  },
  textAreaInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    textAlignVertical: 'top',
    minHeight: 120,
  },
  saveButton: {
    margin: 20,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
});
