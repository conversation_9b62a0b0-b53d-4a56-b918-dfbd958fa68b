import { useEffect, useState } from 'react';
import { Alert, Modal, ScrollView, StyleSheet, TextInput, TouchableOpacity } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { DatabaseManager, Habit, HabitCompletion } from '@/utils/database';

export default function HabitsScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = useThemeColor({}, 'tint');
  const textColor = useThemeColor({}, 'text');
  
  const [habits, setHabits] = useState<Habit[]>([]);
  const [habitCompletions, setHabitCompletions] = useState<HabitCompletion[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newHabitName, setNewHabitName] = useState('');
  const [selectedDays, setSelectedDays] = useState<string[]>([]);
  const [reminderTime, setReminderTime] = useState('09:00');
  const [dbManager] = useState(() => DatabaseManager.getInstance());

  const daysOfWeek = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

  useEffect(() => {
    initDatabase();
  }, []);

  const initDatabase = async () => {
    try {
      await dbManager.initDatabase();
      await loadHabits();
      await loadTodayCompletions();
    } catch (error) {
      console.error('Failed to initialize database:', error);
      Alert.alert('Database Error', 'Failed to initialize database');
    }
  };

  const loadHabits = async () => {
    try {
      const db = dbManager.getDatabase();
      const result = await db.getAllAsync('SELECT * FROM habits ORDER BY created_at DESC') as Habit[];
      setHabits(result);
    } catch (error) {
      console.error('Error loading habits:', error);
    }
  };

  const loadTodayCompletions = async () => {
    try {
      const db = dbManager.getDatabase();
      const today = new Date().toISOString().split('T')[0];
      const result = await db.getAllAsync(
        'SELECT * FROM habit_completions WHERE date = ?',
        [today]
      ) as HabitCompletion[];
      setHabitCompletions(result);
    } catch (error) {
      console.error('Error loading completions:', error);
    }
  };

  const isHabitCompletedToday = (habitId: number) => {
    return habitCompletions.some(completion => 
      completion.habit_id === habitId && completion.completed
    );
  };

  const toggleHabitCompletion = async (habitId: number) => {
    try {
      const db = dbManager.getDatabase();
      const today = new Date().toISOString().split('T')[0];
      const timestamp = new Date().toISOString();
      const isCompleted = isHabitCompletedToday(habitId);

      await db.runAsync(
        'INSERT OR REPLACE INTO habit_completions (habit_id, date, completed, timestamp) VALUES (?, ?, ?, ?)',
        [habitId, today, !isCompleted, timestamp]
      );

      // Update habit streak
      const habit = habits.find(h => h.id === habitId);
      if (habit) {
        let newStreak = habit.streak;
        let newLongestStreak = habit.longest_streak;

        if (!isCompleted) {
          newStreak += 1;
          newLongestStreak = Math.max(newLongestStreak, newStreak);
        } else {
          newStreak = Math.max(0, newStreak - 1);
        }

        await db.runAsync(
          'UPDATE habits SET streak = ?, longest_streak = ? WHERE id = ?',
          [newStreak, newLongestStreak, habitId]
        );
      }

      await loadHabits();
      await loadTodayCompletions();
    } catch (error) {
      console.error('Error toggling habit completion:', error);
      Alert.alert('Error', 'Failed to update habit completion');
    }
  };

  const addNewHabit = async () => {
    if (!newHabitName.trim()) {
      Alert.alert('Missing Name', 'Please enter a habit name.');
      return;
    }
    
    if (selectedDays.length === 0) {
      Alert.alert('Missing Schedule', 'Please select at least one day.');
      return;
    }

    try {
      const db = dbManager.getDatabase();
      const schedule = JSON.stringify(selectedDays);
      const createdAt = new Date().toISOString();

      await db.runAsync(
        'INSERT INTO habits (name, schedule, reminder_time, created_at) VALUES (?, ?, ?, ?)',
        [newHabitName, schedule, reminderTime, createdAt]
      );

      Alert.alert('Habit Added!', 'Your new habit has been created.');
      
      // Reset form and reload
      setNewHabitName('');
      setSelectedDays([]);
      setReminderTime('09:00');
      setShowAddModal(false);
      await loadHabits();
    } catch (error) {
      console.error('Error adding habit:', error);
      Alert.alert('Error', 'Failed to add habit');
    }
  };

  const calculateWeeklyCompletion = () => {
    if (habits.length === 0) return 0;
    const completedHabits = habits.filter(h => isHabitCompletedToday(h.id)).length;
    return Math.round((completedHabits / habits.length) * 100);
  };

  const getHabitSchedule = (habit: Habit) => {
    try {
      return JSON.parse(habit.schedule);
    } catch {
      return [];
    }
  };

  const toggleDaySelection = (day: string) => {
    setSelectedDays(prev => 
      prev.includes(day) 
        ? prev.filter(d => d !== day)
        : [...prev, day]
    );
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">Habits</ThemedText>
        <ThemedText style={styles.subtitle}>Build positive daily routines</ThemedText>
      </ThemedView>

      {/* Progress Overview */}
      <ThemedView style={styles.progressSection}>
        <ThemedText style={styles.sectionTitle}>Today's Progress</ThemedText>
        <ThemedView style={styles.progressBar}>
          <ThemedView 
            style={[
              styles.progressFill,
              { 
                width: `${calculateWeeklyCompletion()}%`,
                backgroundColor: tintColor 
              }
            ]}
          />
        </ThemedView>
        <ThemedText style={styles.progressText}>
          {calculateWeeklyCompletion()}% Complete
        </ThemedText>
      </ThemedView>

      {/* Habits List */}
      <ThemedView style={styles.section}>
        <ThemedText style={styles.sectionTitle}>Your Habits</ThemedText>
        {habits.length === 0 ? (
          <ThemedView style={styles.emptyState}>
            <ThemedText style={styles.emptyText}>No habits yet</ThemedText>
            <ThemedText style={styles.emptySubtext}>Add your first habit to get started</ThemedText>
          </ThemedView>
        ) : (
          habits.map((habit) => (
            <ThemedView key={habit.id} style={styles.habitItem}>
              <TouchableOpacity
                style={[
                  styles.habitCheckbox,
                  isHabitCompletedToday(habit.id) && { backgroundColor: tintColor }
                ]}
                onPress={() => toggleHabitCompletion(habit.id)}
              >
                {isHabitCompletedToday(habit.id) && (
                  <IconSymbol size={16} name="checkmark" color="#FAF9F6" />
                )}
              </TouchableOpacity>
              
              <ThemedView style={styles.habitDetails}>
                <ThemedText style={styles.habitName}>{habit.name}</ThemedText>
                <ThemedText style={styles.habitSchedule}>
                  {getHabitSchedule(habit).join(', ')} • {habit.reminder_time}
                </ThemedText>
                <ThemedText style={styles.habitStreak}>
                  🔥 {habit.streak} day streak (best: {habit.longest_streak})
                </ThemedText>
              </ThemedView>
            </ThemedView>
          ))
        )}
      </ThemedView>

      {/* Add Habit Button */}
      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: tintColor }]}
        onPress={() => setShowAddModal(true)}
      >
        <IconSymbol size={24} name="plus" color="#FAF9F6" />
        <ThemedText style={styles.addButtonText}>Add New Habit</ThemedText>
      </TouchableOpacity>

      {/* Add Habit Modal */}
      <Modal visible={showAddModal} animationType="slide" presentationStyle="pageSheet">
        <ThemedView style={[styles.modalContainer, { backgroundColor }]}>
          <ThemedView style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <ThemedText style={[styles.cancelButton, { color: tintColor }]}>Cancel</ThemedText>
            </TouchableOpacity>
            <ThemedText type="subtitle">New Habit</ThemedText>
            <TouchableOpacity onPress={addNewHabit}>
              <ThemedText style={[styles.saveButton, { color: tintColor }]}>Save</ThemedText>
            </TouchableOpacity>
          </ThemedView>

          <ScrollView style={styles.modalContent}>
            <ThemedView style={styles.inputSection}>
              <ThemedText style={styles.inputLabel}>Habit Name</ThemedText>
              <TextInput
                style={[styles.textInput, { color: textColor, borderColor: tintColor }]}
                placeholder="e.g., Drink 8 glasses of water"
                placeholderTextColor={textColor + '80'}
                value={newHabitName}
                onChangeText={setNewHabitName}
              />
            </ThemedView>

            <ThemedView style={styles.inputSection}>
              <ThemedText style={styles.inputLabel}>Schedule</ThemedText>
              <ThemedView style={styles.daysContainer}>
                {daysOfWeek.map((day) => (
                  <TouchableOpacity
                    key={day}
                    style={[
                      styles.dayButton,
                      selectedDays.includes(day) && { backgroundColor: tintColor }
                    ]}
                    onPress={() => toggleDaySelection(day)}
                  >
                    <ThemedText style={[
                      styles.dayText,
                      selectedDays.includes(day) && { color: 'white' }
                    ]}>
                      {day}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </ThemedView>
            </ThemedView>

            <ThemedView style={styles.inputSection}>
              <ThemedText style={styles.inputLabel}>Reminder Time</ThemedText>
              <TextInput
                style={[styles.textInput, { color: textColor, borderColor: tintColor }]}
                placeholder="09:00"
                placeholderTextColor={textColor + '80'}
                value={reminderTime}
                onChangeText={setReminderTime}
              />
            </ThemedView>
          </ScrollView>
        </ThemedView>
      </Modal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingTop: 60,
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
    marginTop: 8,
  },
  progressSection: {
    padding: 20,
    backgroundColor: 'transparent',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  section: {
    padding: 20,
    backgroundColor: 'transparent',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
    backgroundColor: 'transparent',
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    opacity: 0.7,
  },
  habitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.02)',
  },
  habitCheckbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'rgba(0,0,0,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  habitDetails: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  habitName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  habitSchedule: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 4,
  },
  habitStreak: {
    fontSize: 12,
    opacity: 0.8,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 20,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  addButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 60,
    backgroundColor: 'transparent',
  },
  cancelButton: {
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  inputSection: {
    marginBottom: 24,
    backgroundColor: 'transparent',
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
  },
  daysContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    backgroundColor: 'transparent',
  },
  dayButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  dayText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
