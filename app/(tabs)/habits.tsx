import DateTimePicker from '@react-native-community/datetimepicker';
import { useEffect, useState } from 'react';
import { Alert, Modal, Platform, ScrollView, StyleSheet, TextInput, TouchableOpacity } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { DatabaseManager, Habit, HabitCompletion } from '@/utils/database';
import { NotificationService } from '@/utils/notificationService';

export default function HabitsScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = useThemeColor({}, 'tint');
  const textColor = useThemeColor({}, 'text');

  const [habits, setHabits] = useState<Habit[]>([]);
  const [habitCompletions, setHabitCompletions] = useState<HabitCompletion[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newHabitName, setNewHabitName] = useState('');
  const [selectedDays, setSelectedDays] = useState<string[]>([]);
  const [reminderTimes, setReminderTimes] = useState<string[]>(['09:00']);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [showEditTimePicker, setShowEditTimePicker] = useState(false);
  const [selectedTimeIndex, setSelectedTimeIndex] = useState(0);
  const [selectedHour, setSelectedHour] = useState(9);
  const [selectedMinute, setSelectedMinute] = useState(0);

  const [editTimePickerDate, setEditTimePickerDate] = useState(new Date());
  const [dbManager] = useState(() => DatabaseManager.getInstance());
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingHabit, setEditingHabit] = useState<Habit | null>(null);
  const [editHabitName, setEditHabitName] = useState('');
  const [editSelectedDays, setEditSelectedDays] = useState<string[]>([]);
  const [editReminderTimes, setEditReminderTimes] = useState<string[]>(['09:00']);

  const daysOfWeek = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

  useEffect(() => {
    initDatabase();
    requestNotificationPermissions();
  }, []);

  const requestNotificationPermissions = async () => {
    try {
      const granted = await NotificationService.requestPermissions();
      if (!granted) {
        Alert.alert(
          'Notifications Disabled',
          'To receive habit reminders, please enable notifications in your device settings.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
    }
  };

  const initDatabase = async () => {
    try {
      await dbManager.initDatabase();
      await loadHabits();
      await loadTodayCompletions();
    } catch (error) {
      console.error('Failed to initialize database:', error);
      Alert.alert('Database Error', 'Failed to initialize database');
    }
  };

  const loadHabits = async () => {
    try {
      const db = dbManager.getDatabase();
      const result = await db.getAllAsync('SELECT * FROM habits ORDER BY created_at DESC') as Habit[];
      setHabits(result);
    } catch (error) {
      console.error('Error loading habits:', error);
    }
  };

  const loadTodayCompletions = async () => {
    try {
      const db = dbManager.getDatabase();
      const today = new Date().toISOString().split('T')[0];
      const result = await db.getAllAsync(
        'SELECT * FROM habit_completions WHERE date = ?',
        [today]
      ) as HabitCompletion[];
      setHabitCompletions(result);
    } catch (error) {
      console.error('Error loading completions:', error);
    }
  };

  const isHabitCompletedToday = (habitId: number) => {
    return habitCompletions.some(completion =>
      completion.habit_id === habitId && completion.completed
    );
  };

  const toggleHabitCompletion = async (habitId: number) => {
    try {
      const db = dbManager.getDatabase();
      const today = new Date().toISOString().split('T')[0];
      const timestamp = new Date().toISOString();
      const isCompleted = isHabitCompletedToday(habitId);

      await db.runAsync(
        'INSERT OR REPLACE INTO habit_completions (habit_id, date, completed, timestamp) VALUES (?, ?, ?, ?)',
        [habitId, today, !isCompleted, timestamp]
      );

      // Update habit streak
      const habit = habits.find(h => h.id === habitId);
      if (habit) {
        let newStreak = habit.streak;
        let newLongestStreak = habit.longest_streak;

        if (!isCompleted) {
          newStreak += 1;
          newLongestStreak = Math.max(newLongestStreak, newStreak);
        } else {
          newStreak = Math.max(0, newStreak - 1);
        }

        await db.runAsync(
          'UPDATE habits SET streak = ?, longest_streak = ? WHERE id = ?',
          [newStreak, newLongestStreak, habitId]
        );
      }

      await loadHabits();
      await loadTodayCompletions();
    } catch (error) {
      console.error('Error toggling habit completion:', error);
      Alert.alert('Error', 'Failed to update habit completion');
    }
  };

  const addNewHabit = async () => {
    if (!newHabitName.trim()) {
      Alert.alert('Missing Name', 'Please enter a habit name.');
      return;
    }

    if (selectedDays.length === 0) {
      Alert.alert('Missing Schedule', 'Please select at least one day.');
      return;
    }

    try {
      const db = dbManager.getDatabase();
      const schedule = JSON.stringify(selectedDays);
      const reminderTimesJson = JSON.stringify(reminderTimes);
      const createdAt = new Date().toISOString();

      // Insert the habit first
      const result = await db.runAsync(
        'INSERT INTO habits (name, schedule, reminder_time, created_at) VALUES (?, ?, ?, ?)',
        [newHabitName, schedule, reminderTimesJson, createdAt]
      );

      const habitId = result.lastInsertRowId;

      // Schedule notifications for this habit
      try {
        const notificationIds = await NotificationService.scheduleHabitReminders(
          habitId as number,
          newHabitName,
          reminderTimes,
          selectedDays
        );

        // Update the habit with notification IDs
        if (notificationIds.length > 0) {
          await db.runAsync(
            'UPDATE habits SET notification_ids = ? WHERE id = ?',
            [JSON.stringify(notificationIds), habitId]
          );
        }

        Alert.alert(
          'Habit Added!',
          `Your new habit has been created with ${notificationIds.length} reminder${notificationIds.length !== 1 ? 's' : ''} scheduled.`
        );
      } catch (notificationError) {
        console.error('Error scheduling notifications:', notificationError);
        Alert.alert(
          'Habit Added!',
          'Your habit has been created, but there was an issue setting up reminders. You can try recreating the habit to enable notifications.'
        );
      }

      // Reset form and reload
      setNewHabitName('');
      setSelectedDays([]);
      setReminderTimes(['09:00']);
      setShowAddModal(false);
      await loadHabits();
    } catch (error) {
      console.error('Error adding habit:', error);
      Alert.alert('Error', 'Failed to add habit');
    }
  };

  const calculateWeeklyCompletion = () => {
    if (habits.length === 0) return 0;
    const completedHabits = habits.filter(h => isHabitCompletedToday(h.id)).length;
    return Math.round((completedHabits / habits.length) * 100);
  };

  const getHabitSchedule = (habit: Habit) => {
    try {
      return JSON.parse(habit.schedule);
    } catch {
      return [];
    }
  };

  const getHabitReminderTimes = (habit: Habit) => {
    try {
      const parsed = JSON.parse(habit.reminder_time);
      return Array.isArray(parsed) ? parsed : [habit.reminder_time];
    } catch {
      return [habit.reminder_time];
    }
  };

  const deleteHabit = async (habit: Habit) => {
    Alert.alert(
      'Delete Habit',
      `Are you sure you want to delete "${habit.name}"? This will also cancel all reminders for this habit.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const db = dbManager.getDatabase();

              // Cancel notifications if they exist
              if (habit.notification_ids) {
                try {
                  const notificationIds = JSON.parse(habit.notification_ids);
                  await NotificationService.cancelHabitReminders(notificationIds);
                } catch (error) {
                  console.error('Error canceling notifications:', error);
                }
              }

              // Delete the habit from database
              await db.runAsync('DELETE FROM habits WHERE id = ?', [habit.id]);

              Alert.alert('Habit Deleted', 'The habit and its reminders have been removed.');
              await loadHabits();
            } catch (error) {
              console.error('Error deleting habit:', error);
              Alert.alert('Error', 'Failed to delete habit');
            }
          }
        }
      ]
    );
  };

  const getNotificationCount = (habit: Habit) => {
    try {
      if (!habit.notification_ids) return 0;
      const notificationIds = JSON.parse(habit.notification_ids);
      return Array.isArray(notificationIds) ? notificationIds.length : 0;
    } catch {
      return 0;
    }
  };

  const startEditingHabit = (habit: Habit) => {
    setEditingHabit(habit);
    setEditHabitName(habit.name);
    setEditSelectedDays(getHabitSchedule(habit));
    setEditReminderTimes(getHabitReminderTimes(habit));
    setShowEditModal(true);
  };

  const updateHabit = async () => {
    if (!editingHabit) return;

    if (!editHabitName.trim()) {
      Alert.alert('Missing Name', 'Please enter a habit name.');
      return;
    }

    if (editSelectedDays.length === 0) {
      Alert.alert('Missing Schedule', 'Please select at least one day.');
      return;
    }

    try {
      const db = dbManager.getDatabase();
      const schedule = JSON.stringify(editSelectedDays);
      const reminderTimesJson = JSON.stringify(editReminderTimes);

      // Cancel existing notifications
      if (editingHabit.notification_ids) {
        try {
          const oldNotificationIds = JSON.parse(editingHabit.notification_ids);
          await NotificationService.cancelHabitReminders(oldNotificationIds);
        } catch (error) {
          console.error('Error canceling old notifications:', error);
        }
      }

      // Schedule new notifications
      let newNotificationIds: string[] = [];
      try {
        newNotificationIds = await NotificationService.scheduleHabitReminders(
          editingHabit.id,
          editHabitName,
          editReminderTimes,
          editSelectedDays
        );
      } catch (notificationError) {
        console.error('Error scheduling new notifications:', notificationError);
      }

      // Update the habit in database
      await db.runAsync(
        'UPDATE habits SET name = ?, schedule = ?, reminder_time = ?, notification_ids = ? WHERE id = ?',
        [editHabitName, schedule, reminderTimesJson, JSON.stringify(newNotificationIds), editingHabit.id]
      );

      Alert.alert(
        'Habit Updated!',
        `Your habit has been updated with ${newNotificationIds.length} reminder${newNotificationIds.length !== 1 ? 's' : ''} scheduled.`
      );

      // Reset form and reload
      setShowEditModal(false);
      setEditingHabit(null);
      setEditHabitName('');
      setEditSelectedDays([]);
      setEditReminderTimes(['09:00']);
      await loadHabits();
    } catch (error) {
      console.error('Error updating habit:', error);
      Alert.alert('Error', 'Failed to update habit');
    }
  };

  const toggleEditDaySelection = (day: string) => {
    setEditSelectedDays(prev =>
      prev.includes(day)
        ? prev.filter(d => d !== day)
        : [...prev, day]
    );
  };

  const selectAllEditDays = () => {
    if (editSelectedDays.length === daysOfWeek.length) {
      setEditSelectedDays([]);
    } else {
      setEditSelectedDays([...daysOfWeek]);
    }
  };

  const addEditReminderTime = () => {
    setEditReminderTimes(prev => [...prev, '09:00']);
  };

  const removeEditReminderTime = (index: number) => {
    if (editReminderTimes.length > 1) {
      setEditReminderTimes(prev => prev.filter((_, i) => i !== index));
    }
  };

  const updateEditReminderTime = (index: number, time: string) => {
    setEditReminderTimes(prev => {
      const newTimes = [...prev];
      newTimes[index] = time;
      return newTimes;
    });
  };

  const showEditTimePickerForIndex = (index: number) => {
    console.log('showEditTimePickerForIndex called with index:', index);
    console.log('editReminderTimes:', editReminderTimes);

    setSelectedTimeIndex(index);
    const timeString = editReminderTimes[index];
    console.log('timeString:', timeString);
    const [hours, minutes] = timeString.split(':').map(Number);
    console.log('parsed hours:', hours, 'minutes:', minutes);

    // Create a date object with the current time
    const date = new Date();
    date.setHours(hours);
    date.setMinutes(minutes);
    setEditTimePickerDate(date);

    setShowEditTimePicker(true);
    console.log('showEditTimePicker set to true');
  };


  const toggleDaySelection = (day: string) => {
    setSelectedDays(prev =>
      prev.includes(day)
        ? prev.filter(d => d !== day)
        : [...prev, day]
    );
  };

  const selectAllDays = () => {
    if (selectedDays.length === daysOfWeek.length) {
      // If all days are selected, deselect all
      setSelectedDays([]);
    } else {
      // Select all days
      setSelectedDays([...daysOfWeek]);
    }
  };

  const addReminderTime = () => {
    setReminderTimes(prev => [...prev, '09:00']);
  };

  const removeReminderTime = (index: number) => {
    if (reminderTimes.length > 1) {
      setReminderTimes(prev => prev.filter((_, i) => i !== index));
    }
  };

  const updateReminderTime = (index: number, time: string) => {
    setReminderTimes(prev => {
      const newTimes = [...prev];
      newTimes[index] = time;
      return newTimes;
    });
  };

  const showTimePickerForIndex = (index: number) => {
    setSelectedTimeIndex(index);
    const timeString = reminderTimes[index];
    const [hours, minutes] = timeString.split(':').map(Number);
    setSelectedHour(hours);
    setSelectedMinute(minutes);
    setShowTimePicker(true);
  };

  const confirmTimeSelection = () => {
    const timeString = `${selectedHour.toString().padStart(2, '0')}:${selectedMinute.toString().padStart(2, '0')}`;
    console.log('confirmTimeSelection called for add context');
    console.log('timeString:', timeString, 'selectedTimeIndex:', selectedTimeIndex);

    updateReminderTime(selectedTimeIndex, timeString);
    console.log('Updated add reminder time');
    setShowTimePicker(false);
  };

  const cancelTimeSelection = () => {
    setShowTimePicker(false);
  };

  const onEditTimeChange = (event: any, selectedDate?: Date) => {
    console.log('onEditTimeChange called', event, selectedDate);

    if (Platform.OS === 'android') {
      setShowEditTimePicker(false);
    }

    if (selectedDate && event.type !== 'dismissed') {
      const hours = selectedDate.getHours();
      const minutes = selectedDate.getMinutes();
      const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;

      console.log('Selected time:', timeString, 'for index:', selectedTimeIndex);
      updateEditReminderTime(selectedTimeIndex, timeString);

      if (Platform.OS === 'ios') {
        setShowEditTimePicker(false);
      }
    } else if (Platform.OS === 'ios') {
      setShowEditTimePicker(false);
    }
  };

  const hours = Array.from({ length: 24 }, (_, i) => i);
  const minutes = Array.from({ length: 60 }, (_, i) => i);

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">Habits</ThemedText>
        <ThemedText style={styles.subtitle}>Build positive daily routines</ThemedText>
      </ThemedView>

      {/* Progress Overview */}
      <ThemedView style={styles.progressSection}>
        <ThemedText style={styles.sectionTitle}>Today's Progress</ThemedText>
        <ThemedView style={styles.progressBar}>
          <ThemedView
            style={[
              styles.progressFill,
              {
                width: `${calculateWeeklyCompletion()}%`,
                backgroundColor: tintColor
              }
            ]}
          />
        </ThemedView>
        <ThemedText style={styles.progressText}>
          {calculateWeeklyCompletion()}% Complete
        </ThemedText>
      </ThemedView>

      {/* Habits List */}
      <ThemedView style={styles.section}>
        <ThemedText style={styles.sectionTitle}>Your Habits</ThemedText>
        {habits.length === 0 ? (
          <ThemedView style={styles.emptyState}>
            <ThemedText style={styles.emptyText}>No habits yet</ThemedText>
            <ThemedText style={styles.emptySubtext}>Add your first habit to get started</ThemedText>
          </ThemedView>
        ) : (
          habits.map((habit) => (
            <ThemedView key={habit.id} style={styles.habitItem}>
              <TouchableOpacity
                style={[
                  styles.habitCheckbox,
                  isHabitCompletedToday(habit.id) && { backgroundColor: tintColor }
                ]}
                onPress={() => toggleHabitCompletion(habit.id)}
              >
                {isHabitCompletedToday(habit.id) && (
                  <IconSymbol size={16} name="checkmark" color="#FAF9F6" />
                )}
              </TouchableOpacity>

              <ThemedView style={styles.habitDetails}>
                <ThemedText style={styles.habitName}>{habit.name}</ThemedText>
                <ThemedText style={styles.habitSchedule}>
                  {getHabitSchedule(habit).join(', ')} • {getHabitReminderTimes(habit).join(', ')}
                </ThemedText>
                <ThemedView style={styles.habitMetaRow}>
                  <ThemedText style={styles.habitStreak}>
                    🔥 {habit.streak} day streak (best: {habit.longest_streak})
                  </ThemedText>
                  <ThemedView style={styles.notificationBadge}>
                    <IconSymbol size={12} name="bell" color={tintColor} />
                    <ThemedText style={[styles.notificationCount, { color: tintColor }]}>
                      {getNotificationCount(habit)}
                    </ThemedText>
                  </ThemedView>
                </ThemedView>
              </ThemedView>

              <ThemedView style={styles.habitActions}>
                <TouchableOpacity
                  style={styles.editButton}
                  onPress={() => startEditingHabit(habit)}
                >
                  <IconSymbol size={18} name="pencil" color={tintColor} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={() => deleteHabit(habit)}
                >
                  <IconSymbol size={18} name="trash" color="#E07A5F" />
                </TouchableOpacity>
              </ThemedView>
            </ThemedView>
          ))
        )}
      </ThemedView>

      {/* Add Habit Button */}
      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: tintColor }]}
        onPress={() => setShowAddModal(true)}
      >
        <IconSymbol size={24} name="plus" color="#FAF9F6" />
        <ThemedText style={styles.addButtonText}>Add New Habit</ThemedText>
      </TouchableOpacity>

      {/* Edit Habit Modal */}
      <Modal visible={showEditModal} animationType="slide" presentationStyle="pageSheet">
        <ThemedView style={[styles.modalContainer, { backgroundColor }]}>
          <ThemedView style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowEditModal(false)}>
              <ThemedText style={[styles.cancelButton, { color: tintColor }]}>Cancel</ThemedText>
            </TouchableOpacity>
            <ThemedText type="subtitle">Edit Habit</ThemedText>
            <TouchableOpacity onPress={updateHabit}>
              <ThemedText style={[styles.saveButton, { color: tintColor }]}>Save</ThemedText>
            </TouchableOpacity>
          </ThemedView>

          <ScrollView style={styles.modalContent}>
            <ThemedView style={styles.inputSection}>
              <ThemedText style={styles.inputLabel}>Habit Name</ThemedText>
              <TextInput
                style={[styles.textInput, { color: textColor, borderColor: tintColor }]}
                placeholder="e.g., Drink 8 glasses of water"
                placeholderTextColor={textColor + '80'}
                value={editHabitName}
                onChangeText={setEditHabitName}
              />
            </ThemedView>

            <ThemedView style={styles.inputSection}>
              <ThemedText style={styles.inputLabel}>Schedule</ThemedText>
              <ThemedView style={styles.daysContainer}>
                {daysOfWeek.map((day) => (
                  <TouchableOpacity
                    key={day}
                    style={[
                      styles.dayButton,
                      editSelectedDays.includes(day) && { backgroundColor: tintColor }
                    ]}
                    onPress={() => toggleEditDaySelection(day)}
                  >
                    <ThemedText style={[
                      styles.dayText,
                      editSelectedDays.includes(day) && { color: 'white' }
                    ]}>
                      {day}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </ThemedView>
              <TouchableOpacity
                style={[
                  styles.everydayButton,
                  { borderColor: tintColor },
                  editSelectedDays.length === daysOfWeek.length && { backgroundColor: tintColor }
                ]}
                onPress={selectAllEditDays}
              >
                <ThemedText style={[
                  styles.everydayText,
                  { color: tintColor },
                  editSelectedDays.length === daysOfWeek.length && { color: 'white' }
                ]}>
                  {editSelectedDays.length === daysOfWeek.length ? 'Deselect All' : 'Everyday'}
                </ThemedText>
              </TouchableOpacity>
            </ThemedView>

            <ThemedView style={styles.inputSection}>
              <ThemedView style={styles.reminderHeader}>
                <ThemedText style={styles.inputLabel}>Reminder Times</ThemedText>
                <TouchableOpacity
                  style={[styles.addReminderButton, { borderColor: tintColor }]}
                  onPress={addEditReminderTime}
                >
                  <IconSymbol size={16} name="plus" color={tintColor} />
                  <ThemedText style={[styles.addReminderText, { color: tintColor }]}>Add</ThemedText>
                </TouchableOpacity>
              </ThemedView>
              {editReminderTimes.map((time, index) => (
                <ThemedView key={index} style={styles.reminderTimeRow}>
                  <TouchableOpacity
                    style={[styles.timeSelector, { borderColor: tintColor }]}
                    onPress={() => showEditTimePickerForIndex(index)}
                  >
                    <IconSymbol size={20} name="clock" color={tintColor} />
                    <ThemedText style={[styles.timeText, { color: textColor }]}>{time}</ThemedText>
                  </TouchableOpacity>
                  {editReminderTimes.length > 1 && (
                    <TouchableOpacity
                      style={[styles.removeReminderButton, { borderColor: '#E07A5F' }]}
                      onPress={() => removeEditReminderTime(index)}
                    >
                      <IconSymbol size={16} name="minus" color="#E07A5F" />
                    </TouchableOpacity>
                  )}
                </ThemedView>
              ))}
            </ThemedView>
          </ScrollView>
        </ThemedView>
      </Modal>

      {/* Add Habit Modal */}
      <Modal visible={showAddModal} animationType="slide" presentationStyle="pageSheet">
        <ThemedView style={[styles.modalContainer, { backgroundColor }]}>
          <ThemedView style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <ThemedText style={[styles.cancelButton, { color: tintColor }]}>Cancel</ThemedText>
            </TouchableOpacity>
            <ThemedText type="subtitle">New Habit</ThemedText>
            <TouchableOpacity onPress={addNewHabit}>
              <ThemedText style={[styles.saveButton, { color: tintColor }]}>Save</ThemedText>
            </TouchableOpacity>
          </ThemedView>

          <ScrollView style={styles.modalContent}>
            <ThemedView style={styles.inputSection}>
              <ThemedText style={styles.inputLabel}>Habit Name</ThemedText>
              <TextInput
                style={[styles.textInput, { color: textColor, borderColor: tintColor }]}
                placeholder="e.g., Drink 8 glasses of water"
                placeholderTextColor={textColor + '80'}
                value={newHabitName}
                onChangeText={setNewHabitName}
              />
            </ThemedView>

            <ThemedView style={styles.inputSection}>
              <ThemedText style={styles.inputLabel}>Schedule</ThemedText>
              <ThemedView style={styles.daysContainer}>
                {daysOfWeek.map((day) => (
                  <TouchableOpacity
                    key={day}
                    style={[
                      styles.dayButton,
                      selectedDays.includes(day) && { backgroundColor: tintColor }
                    ]}
                    onPress={() => toggleDaySelection(day)}
                  >
                    <ThemedText style={[
                      styles.dayText,
                      selectedDays.includes(day) && { color: 'white' }
                    ]}>
                      {day}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </ThemedView>
              <TouchableOpacity
                style={[
                  styles.everydayButton,
                  { borderColor: tintColor },
                  selectedDays.length === daysOfWeek.length && { backgroundColor: tintColor }
                ]}
                onPress={selectAllDays}
              >
                <ThemedText style={[
                  styles.everydayText,
                  { color: tintColor },
                  selectedDays.length === daysOfWeek.length && { color: 'white' }
                ]}>
                  {selectedDays.length === daysOfWeek.length ? 'Deselect All' : 'Everyday'}
                </ThemedText>
              </TouchableOpacity>
            </ThemedView>

            <ThemedView style={styles.inputSection}>
              <ThemedView style={styles.reminderHeader}>
                <ThemedText style={styles.inputLabel}>Reminder Times</ThemedText>
                <TouchableOpacity
                  style={[styles.addReminderButton, { borderColor: tintColor }]}
                  onPress={addReminderTime}
                >
                  <IconSymbol size={16} name="plus" color={tintColor} />
                  <ThemedText style={[styles.addReminderText, { color: tintColor }]}>Add</ThemedText>
                </TouchableOpacity>
              </ThemedView>
              {reminderTimes.map((time, index) => (
                <ThemedView key={index} style={styles.reminderTimeRow}>
                  <TouchableOpacity
                    style={[styles.timeSelector, { borderColor: tintColor }]}
                    onPress={() => showTimePickerForIndex(index)}
                  >
                    <IconSymbol size={20} name="clock" color={tintColor} />
                    <ThemedText style={[styles.timeText, { color: textColor }]}>{time}</ThemedText>
                  </TouchableOpacity>
                  {reminderTimes.length > 1 && (
                    <TouchableOpacity
                      style={[styles.removeReminderButton, { borderColor: '#E07A5F' }]}
                      onPress={() => removeReminderTime(index)}
                    >
                      <IconSymbol size={16} name="minus" color="#E07A5F" />
                    </TouchableOpacity>
                  )}
                </ThemedView>
              ))}
            </ThemedView>
          </ScrollView>
        </ThemedView>
      </Modal>

      {/* Custom Time Picker Modal - Shared between Add and Edit */}
      <Modal visible={showTimePicker} animationType="slide" presentationStyle="overFullScreen">
        <ThemedView style={[styles.timePickerContainer, { backgroundColor }]}>
          <ThemedView style={styles.timePickerHeader}>
            <TouchableOpacity onPress={cancelTimeSelection}>
              <ThemedText style={[styles.cancelButton, { color: tintColor }]}>Cancel</ThemedText>
            </TouchableOpacity>
            <ThemedText type="subtitle">Select Time</ThemedText>
            <TouchableOpacity onPress={confirmTimeSelection}>
              <ThemedText style={[styles.saveButton, { color: tintColor }]}>Done</ThemedText>
            </TouchableOpacity>
          </ThemedView>

          <ThemedView style={styles.timePickerContent}>
            <ThemedView style={styles.timePickerRow}>
              <ThemedView style={styles.timeColumn}>
                <ThemedText style={styles.timeColumnLabel}>Hour</ThemedText>
                <ScrollView style={styles.timeScrollView} showsVerticalScrollIndicator={false}>
                  {hours.map((hour) => (
                    <TouchableOpacity
                      key={hour}
                      style={[
                        styles.timeOption,
                        selectedHour === hour && { backgroundColor: tintColor }
                      ]}
                      onPress={() => setSelectedHour(hour)}
                    >
                      <ThemedText style={[
                        styles.timeOptionText,
                        selectedHour === hour && { color: 'white' }
                      ]}>
                        {hour.toString().padStart(2, '0')}
                      </ThemedText>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </ThemedView>

              <ThemedView style={styles.timeColumn}>
                <ThemedText style={styles.timeColumnLabel}>Minute</ThemedText>
                <ScrollView style={styles.timeScrollView} showsVerticalScrollIndicator={false}>
                  {minutes.filter(m => m % 5 === 0).map((minute) => (
                    <TouchableOpacity
                      key={minute}
                      style={[
                        styles.timeOption,
                        selectedMinute === minute && { backgroundColor: tintColor }
                      ]}
                      onPress={() => setSelectedMinute(minute)}
                    >
                      <ThemedText style={[
                        styles.timeOptionText,
                        selectedMinute === minute && { color: 'white' }
                      ]}>
                        {minute.toString().padStart(2, '0')}
                      </ThemedText>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </ThemedView>
            </ThemedView>
          </ThemedView>
        </ThemedView>
      </Modal>

      {/* Native DateTimePicker for Edit Context */}
      {showEditTimePicker && (
        <DateTimePicker
          value={editTimePickerDate}
          mode="time"
          is24Hour={true}
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={onEditTimeChange}
        />
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    marginBottom: 30,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
    marginTop: 5,
  },
  progressSection: {
    marginBottom: 30,
    padding: 20,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 4,
    marginBottom: 10,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  section: {
    marginBottom: 30,
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 5,
  },
  emptySubtext: {
    fontSize: 14,
    opacity: 0.7,
  },
  habitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    marginBottom: 10,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  habitCheckbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#ccc',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
  },
  habitDetails: {
    flex: 1,
  },
  habitName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 5,
  },
  habitSchedule: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 5,
  },
  habitMetaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  habitStreak: {
    fontSize: 14,
    fontWeight: '500',
  },
  notificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  notificationCount: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  habitActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  editButton: {
    padding: 8,
  },
  deleteButton: {
    padding: 8,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 12,
    marginBottom: 20,
  },
  addButtonText: {
    color: '#FAF9F6',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  cancelButton: {
    fontSize: 16,
  },
  saveButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  inputSection: {
    marginBottom: 25,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 10,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  daysContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 15,
  },
  dayButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ccc',
  },
  dayText: {
    fontSize: 14,
    fontWeight: '500',
  },
  everydayButton: {
    alignSelf: 'flex-start',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  everydayText: {
    fontSize: 14,
    fontWeight: '500',
  },
  reminderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  addReminderButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
  },
  addReminderText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  reminderTimeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  timeSelector: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginRight: 10,
  },
  timeText: {
    fontSize: 16,
    marginLeft: 8,
  },
  removeReminderButton: {
    padding: 8,
    borderWidth: 1,
    borderRadius: 6,
  },
  timePickerContainer: {
    flex: 1,
    zIndex: 9999,
  },
  timePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  timePickerContent: {
    flex: 1,
    padding: 20,
  },
  timePickerRow: {
    flexDirection: 'row',
    gap: 20,
  },
  timeColumn: {
    flex: 1,
  },
  timeColumnLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 10,
    textAlign: 'center',
  },
  timeScrollView: {
    maxHeight: 200,
  },
  timeOption: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 5,
    alignItems: 'center',
  },
  timeOptionText: {
    fontSize: 16,
  },
});