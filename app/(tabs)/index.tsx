import { StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Spacing } from '@/constants/Spacing';

export default function HomeScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = useThemeColor({}, 'tint');
  const cardColor = useThemeColor({}, 'card');
  const secondaryColor = useThemeColor({}, 'secondary');
  const accentColor = useThemeColor({}, 'accent');
  
  const currentDate = new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">Your Safe Space</ThemedText>
        <ThemedText type="caption" style={styles.dateText}>{currentDate}</ThemedText>
      </ThemedView>

      {/* Daily Check-in Card */}
      <Card style={styles.checkInCard}>
        <TouchableOpacity 
          style={styles.checkInContent}
          onPress={() => router.push('/(tabs)/mood')}
          activeOpacity={0.7}
        >
          <IconSymbol size={32} name="face.smiling" color={accentColor} />
          <ThemedView style={styles.checkInText}>
            <ThemedText type="subtitle">How are you feeling today?</ThemedText>
            <ThemedText type="caption" style={styles.checkInSubtext}>Tap to log your mood</ThemedText>
          </ThemedView>
          <IconSymbol size={20} name="chevron.right" color={accentColor} />
        </TouchableOpacity>
      </Card>

      {/* Quick Actions */}
      <ThemedView style={styles.quickActions}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>Quick Actions</ThemedText>
        
        <ThemedView style={styles.actionGrid}>
          <Card style={styles.actionCard}>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => router.push('/(tabs)/journal')}
              activeOpacity={0.7}
            >
              <IconSymbol size={24} name="book.fill" color={accentColor} />
              <ThemedText type="defaultSemiBold" style={styles.actionText}>Journal</ThemedText>
            </TouchableOpacity>
          </Card>

          <Card style={styles.actionCard}>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => router.push('/(tabs)/breathe')}
              activeOpacity={0.7}
            >
              <IconSymbol size={24} name="lungs.fill" color={accentColor} />
              <ThemedText type="defaultSemiBold" style={styles.actionText}>Breathe</ThemedText>
            </TouchableOpacity>
          </Card>

          <Card style={styles.actionCard}>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => router.push('/(tabs)/habits')}
              activeOpacity={0.7}
            >
              <IconSymbol size={24} name="checkmark.circle.fill" color={accentColor} />
              <ThemedText type="defaultSemiBold" style={styles.actionText}>Habits</ThemedText>
            </TouchableOpacity>
          </Card>

          <Card style={styles.actionCard}>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => router.push('/(tabs)/more')}
              activeOpacity={0.7}
            >
              <IconSymbol size={24} name="ellipsis.circle.fill" color={accentColor} />
              <ThemedText type="defaultSemiBold" style={styles.actionText}>More</ThemedText>
            </TouchableOpacity>
          </Card>

        </ThemedView>
      </ThemedView>

      {/* Call to Action */}
      <ThemedView style={styles.ctaSection}>
        <Button
          title="View Dashboard"
          onPress={() => router.push('/dashboard')}
          variant="primary"
          size="large"
          style={styles.ctaButton}
          icon={<IconSymbol size={20} name="chart.bar.fill" color="#FAF9F6" />}
        />
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60,
  },
  header: {
    padding: Spacing.lg,
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  dateText: {
    opacity: 0.7,
    marginTop: Spacing.xs,
  },
  checkInCard: {
    margin: Spacing.lg,
  },
  checkInContent: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  checkInText: {
    flex: 1,
    marginLeft: Spacing.md,
    backgroundColor: 'transparent',
  },
  checkInSubtext: {
    opacity: 0.7,
    marginTop: Spacing.xs,
  },
  quickActions: {
    padding: Spacing.lg,
    backgroundColor: 'transparent',
  },
  sectionTitle: {
    marginBottom: Spacing.md,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    backgroundColor: 'transparent',
  },
  actionCard: {
    width: '48%',
    padding: 0, // Card component handles padding
    marginBottom: Spacing.md,
  },
  actionButton: {
    alignItems: 'center',
    padding: Spacing.lg,
  },
  actionText: {
    marginTop: Spacing.sm,
  },
  ctaSection: {
    padding: Spacing.lg,
    backgroundColor: 'transparent',
  },
  ctaButton: {
    width: '100%',
  },
});
