import { useState } from 'react';
import { Alert, ScrollView, StyleSheet, TextInput, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import * as SQLite from 'expo-sqlite';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Spacing } from '@/constants/Spacing';

export default function MoodScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = useThemeColor({}, 'tint');
  const textColor = useThemeColor({}, 'text');
  const cardColor = useThemeColor({}, 'card');
  const secondaryColor = useThemeColor({}, 'secondary');
  const accentColor = useThemeColor({}, 'accent');
  
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<string[]>([]);
  const [currentAnswer, setCurrentAnswer] = useState('');
  const [isCompleted, setIsCompleted] = useState(false);

  const questions = [
    {
      question: "Unpleasant or pleasant?",
      type: "choice",
      options: ["Unpleasant", "Pleasant"]
    },
    {
      question: "Mild or intense?",
      type: "choice", 
      options: ["Mild", "Intense"]
    },
    {
      question: "What most contributed to your current emotional state?",
      type: "text"
    },
    {
      question: "How would you describe your thoughts or reactions to this feeling?",
      type: "text"
    },
    {
      question: "How often have you felt this way in the last month?",
      type: "choice",
      options: ["Rarely", "Sometimes", "Often", "Very Often"]
    },
    {
      question: "Could you be feeling?",
      type: "emotion_choice"
    },
    {
      question: "Why are you feeling this way?",
      type: "text"
    }
  ];

  const getEmotionOptions = () => {
    const pleasantness = answers[0]; // "Unpleasant" or "Pleasant"
    const intensity = answers[1]; // "Mild" or "Intense"

    if (pleasantness === "Unpleasant") {
      if (intensity === "Intense") {
        return ["Depression", "Anxiety", "Anger", "Rage", "Panic", "Despair", "Fury", "Terror"];
      } else {
        return ["Sadness", "Worry", "Irritation", "Disappointment", "Concern", "Melancholy", "Unease", "Discontent"];
      }
    } else {
      if (intensity === "Intense") {
        return ["Joy", "Excitement", "Euphoria", "Elation", "Ecstasy", "Bliss", "Enthusiasm", "Passion"];
      } else {
        return ["Contentment", "Calm", "Satisfaction", "Peace", "Serenity", "Gratitude", "Hope", "Comfort"];
      }
    }
  };

  const handleAnswer = (answer: string) => {
    const newAnswers = [...answers, answer];
    setAnswers(newAnswers);
    setCurrentAnswer('');
    
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      setIsCompleted(true);
      saveMoodLog(newAnswers);
    }
  };

  const saveMoodLog = async (finalAnswers: string[]) => {
    try {
      const db = await SQLite.openDatabaseAsync('mood_tracker.db');
      
      // Create table if it doesn't exist
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS mood_logs (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          date TEXT NOT NULL,
          pleasantness TEXT,
          intensity TEXT,
          main_contributor TEXT,
          thoughts_reactions TEXT,
          frequency TEXT,
          possible_feelings TEXT,
          reasons TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
      `);

      // Insert the mood log
      await db.runAsync(
        `INSERT INTO mood_logs (date, pleasantness, intensity, main_contributor, thoughts_reactions, frequency, possible_feelings, reasons) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          new Date().toISOString().split('T')[0],
          finalAnswers[0] || '',
          finalAnswers[1] || '',
          finalAnswers[2] || '',
          finalAnswers[3] || '',
          finalAnswers[4] || '',
          finalAnswers[5] || '',
          finalAnswers[6] || ''
        ]
      );

      Alert.alert(
        'Mood Log Saved',
        'Your mood assessment has been saved successfully.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error saving mood log:', error);
      Alert.alert('Error', 'Failed to save mood log. Please try again.');
    }
  };

  const resetAssessment = () => {
    setCurrentQuestion(0);
    setAnswers([]);
    setCurrentAnswer('');
    setIsCompleted(false);
  };

  const renderQuestion = () => {
    const question = questions[currentQuestion];
    
    return (
      <ThemedView style={[styles.questionContainer, { backgroundColor: cardColor }]}>
        <ThemedText type="subtitle" style={styles.questionText}>
          {question.question}
        </ThemedText>
        
        {question.type === "choice" ? (
          <ThemedView style={styles.choiceContainer}>
            {question.options?.map((option, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.choiceButton, { borderColor: tintColor }]}
                onPress={() => handleAnswer(option)}
              >
                <ThemedText style={styles.choiceText}>{option}</ThemedText>
              </TouchableOpacity>
            ))}
          </ThemedView>
        ) : question.type === "emotion_choice" ? (
          <ThemedView style={styles.emotionContainer}>
            {getEmotionOptions().map((emotion, index) => (
              <TouchableOpacity
                key={index}
                style={[styles.emotionButton, { borderColor: tintColor }]}
                onPress={() => handleAnswer(emotion)}
              >
                <ThemedText style={styles.emotionText}>{emotion}</ThemedText>
              </TouchableOpacity>
            ))}
          </ThemedView>
        ) : (
          <ThemedView style={styles.textInputContainer}>
            <TextInput
              style={[styles.textInput, { borderColor: tintColor, color: textColor }]}
              value={currentAnswer}
              onChangeText={setCurrentAnswer}
              placeholder="Type your answer here..."
              placeholderTextColor={textColor + '80'}
              multiline
              numberOfLines={4}
            />
            <TouchableOpacity
              style={[styles.submitButton, { backgroundColor: tintColor }]}
              onPress={() => handleAnswer(currentAnswer)}
              disabled={!currentAnswer.trim()}
            >
              <ThemedText style={styles.submitButtonText}>Next</ThemedText>
            </TouchableOpacity>
          </ThemedView>
        )}
      </ThemedView>
    );
  };

  const goBackToQuestion = (questionIndex: number) => {
    // Remove answers from the selected question onwards
    const newAnswers = answers.slice(0, questionIndex);
    setAnswers(newAnswers);
    setCurrentQuestion(questionIndex);
    setCurrentAnswer('');
    setIsCompleted(false);
  };

  const renderPreviousAnswers = () => {
    return answers.map((answer, index) => (
      <TouchableOpacity
        key={index}
        style={[styles.previousAnswer, { backgroundColor: secondaryColor }]}
        onPress={() => goBackToQuestion(index)}
        activeOpacity={0.7}
      >
        <ThemedView style={styles.previousAnswerContent}>
          <ThemedView style={styles.previousAnswerTextContainer}>
            <ThemedText style={styles.previousQuestionText}>
              {questions[index].question}
            </ThemedText>
            <ThemedText style={styles.previousAnswerText}>{answer}</ThemedText>
          </ThemedView>
          <IconSymbol 
            size={16} 
            name="pencil" 
            color={textColor + '60'} 
            style={styles.editIcon}
          />
        </ThemedView>
      </TouchableOpacity>
    ));
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">Mood Assessment</ThemedText>
        <ThemedText style={styles.progressText}>
          {isCompleted ? 'Completed' : `Question ${currentQuestion + 1} of ${questions.length}`}
        </ThemedText>
      </ThemedView>

      {/* Previous answers */}
      {renderPreviousAnswers()}

      {/* Current question or completion message */}
      {!isCompleted ? (
        renderQuestion()
      ) : (
        <ThemedView style={[styles.completionContainer, { backgroundColor: cardColor }]}>
          <IconSymbol size={48} name="checkmark.circle.fill" color={accentColor} />
          <ThemedText type="subtitle" style={styles.completionText}>
            Assessment Complete!
          </ThemedText>
          <ThemedText style={styles.completionSubtext}>
            Your mood log has been saved successfully.
          </ThemedText>
        </ThemedView>
      )}

      {/* Action buttons */}
      <ThemedView style={styles.actionButtons}>
        <Button
          title="View Calendar"
          onPress={() => router.push('/mood-history')}
          variant="primary"
          style={styles.actionButton}
          icon={<IconSymbol size={20} name="calendar" color="#FAF9F6" />}
        />

        <Button
          title="View Chart"
          onPress={() => router.push('/mood-chart')}
          variant="secondary"
          style={styles.actionButton}
          icon={<IconSymbol size={20} name="chart.bar.fill" color="#FAF9F6" />}
        />
      </ThemedView>

      {isCompleted && (
        <TouchableOpacity
          style={[styles.resetButton, { borderColor: tintColor }]}
          onPress={resetAssessment}
        >
          <ThemedText style={[styles.resetButtonText, { color: tintColor }]}>
            Start New Assessment
          </ThemedText>
        </TouchableOpacity>
      )}
      
      {/* Bottom spacing for tab bar */}
      <ThemedView style={styles.bottomSpacer} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60,
  },
  header: {
    padding: 20,
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  progressText: {
    fontSize: 14,
    opacity: 0.7,
    marginTop: 4,
  },
  questionContainer: {
    margin: 20,
    padding: 20,
    borderRadius: 16,
  },
  questionText: {
    marginBottom: 20,
    textAlign: 'center',
  },
  choiceContainer: {
    backgroundColor: 'transparent',
  },
  choiceButton: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    marginBottom: 12,
    alignItems: 'center',
  },
  choiceText: {
    fontSize: 16,
    fontWeight: '600',
  },
  emotionContainer: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  emotionButton: {
    width: '48%',
    padding: 12,
    borderRadius: 12,
    borderWidth: 2,
    marginBottom: 12,
    alignItems: 'center',
  },
  emotionText: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  textInputContainer: {
    backgroundColor: 'transparent',
  },
  textInput: {
    borderWidth: 2,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    textAlignVertical: 'top',
    minHeight: 100,
    marginBottom: 16,
  },
  submitButton: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  previousAnswer: {
    margin: 20,
    marginBottom: 10,
    padding: 16,
    borderRadius: 12,
  },
  previousAnswerContent: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  previousAnswerTextContainer: {
    backgroundColor: 'transparent',
    flex: 1,
  },
  previousQuestionText: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    opacity: 0.8,
  },
  previousAnswerText: {
    fontSize: 16,
  },
  editIcon: {
    marginLeft: 12,
    marginTop: 2,
  },
  completionContainer: {
    margin: 20,
    padding: 30,
    borderRadius: 16,
    alignItems: 'center',
  },
  completionText: {
    marginTop: 16,
    marginBottom: 8,
  },
  completionSubtext: {
    opacity: 0.7,
    textAlign: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginTop: 20,
    backgroundColor: 'transparent',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 8,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  resetButton: {
    margin: 20,
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    alignItems: 'center',
  },
  resetButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  bottomSpacer: {
    height: 100,
    backgroundColor: 'transparent',
  },
});