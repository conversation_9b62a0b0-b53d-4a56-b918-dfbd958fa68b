import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert, Linking, Modal, ScrollView, StyleSheet, TextInput, TouchableOpacity } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Affirmation, DatabaseManager } from '@/utils/database';

export default function MoreScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = useThemeColor({}, 'tint');
  const textColor = useThemeColor({}, 'text');
  
  const [showDistractionModal, setShowDistractionModal] = useState(false);
  const [showAffirmations, setShowAffirmations] = useState(false);
  const [showEmotionWheel, setShowEmotionWheel] = useState(false);
  const [showSafetyPlan, setShowSafetyPlan] = useState(false);
  
  const [distractionWhat, setDistractionWhat] = useState('');
  const [distractionWhen, setDistractionWhen] = useState('');
  const [distractionWhy, setDistractionWhy] = useState('');
  
  const [customAffirmation, setCustomAffirmation] = useState('');
  const [currentAffirmation, setCurrentAffirmation] = useState('');
  const [affirmations, setAffirmations] = useState<Affirmation[]>([]);
  
  const [selectedEmotion, setSelectedEmotion] = useState('');
  const [selectedSecondaryEmotion, setSelectedSecondaryEmotion] = useState('');
  const [emotionIntensity, setEmotionIntensity] = useState<'low' | 'medium' | 'high' | ''>('');
  const [emotionReflection, setEmotionReflection] = useState('');
  
  const [dbManager] = useState(() => DatabaseManager.getInstance());

  const primaryEmotions = ['Joy', 'Sadness', 'Anger', 'Fear', 'Surprise', 'Disgust', 'Trust', 'Anticipation'];
  const emotionDetails: Record<string, string[]> = {
    Joy: ['Happy', 'Excited', 'Grateful', 'Peaceful', 'Content', 'Optimistic', 'Elated', 'Cheerful'],
    Sadness: ['Disappointed', 'Lonely', 'Hurt', 'Grief', 'Melancholy', 'Despair', 'Dejected', 'Sorrowful'],
    Anger: ['Frustrated', 'Irritated', 'Furious', 'Resentful', 'Annoyed', 'Outraged', 'Livid', 'Indignant'],
    Fear: ['Anxious', 'Worried', 'Scared', 'Nervous', 'Terrified', 'Panicked', 'Apprehensive', 'Uneasy'],
    Surprise: ['Amazed', 'Confused', 'Startled', 'Bewildered', 'Astonished', 'Perplexed', 'Shocked', 'Stunned'],
    Disgust: ['Revolted', 'Appalled', 'Horrified', 'Nauseated', 'Repulsed', 'Sickened', 'Contemptuous', 'Loathing'],
    Trust: ['Confident', 'Secure', 'Accepting', 'Admiring', 'Respectful', 'Faithful', 'Devoted', 'Assured'],
    Anticipation: ['Eager', 'Hopeful', 'Excited', 'Expectant', 'Curious', 'Interested', 'Vigilant', 'Alert']
  };

  const emotionColors: Record<string, string> = {
    Joy: '#FFD700',
    Sadness: '#4682B4',
    Anger: '#DC143C',
    Fear: '#9370DB',
    Surprise: '#FF8C00',
    Disgust: '#228B22',
    Trust: '#20B2AA',
    Anticipation: '#FF69B4'
  };

  const calmingStrategies = [
    'Take 5 deep breaths',
    'Count to 10 slowly',
    'Listen to calming music',
    'Go for a walk',
    'Practice progressive muscle relaxation',
    'Call a trusted friend or family member',
    'Write in a journal',
    'Use a grounding technique (5-4-3-2-1)',
    'Take a warm bath or shower',
    'Practice mindfulness meditation'
  ];

  const emergencyContacts = [
    { id: 1, name: 'Crisis Text Line', phone: '741741' },
    { id: 2, name: 'National Suicide Prevention Lifeline', phone: '988' },
    { id: 3, name: 'Emergency Services', phone: '911' },
    { id: 4, name: 'Trusted Friend/Family', phone: 'Add your contact' }
  ];

  useEffect(() => {
    initDatabase();
  }, []);

  const initDatabase = async () => {
    try {
      await dbManager.initDatabase();
      await loadAffirmations();
    } catch (error) {
      console.error('Failed to initialize database:', error);
      Alert.alert('Database Error', 'Failed to initialize database');
    }
  };

  const loadAffirmations = async () => {
    try {
      const db = dbManager.getDatabase();
      const result = await db.getAllAsync(
        'SELECT * FROM affirmations ORDER BY times_used DESC, created_at DESC'
      ) as Affirmation[];
      setAffirmations(result);
    } catch (error) {
      console.error('Error loading affirmations:', error);
    }
  };

  const addDistraction = async () => {
    if (!distractionWhat.trim()) {
      Alert.alert('Missing Information', 'Please describe what distracted you.');
      return;
    }

    try {
      const db = dbManager.getDatabase();
      const now = new Date();
      const date = now.toISOString().split('T')[0];
      const timestamp = now.toISOString();

      await db.runAsync(
        'INSERT INTO distraction_entries (what, when_occurred, why, date, timestamp) VALUES (?, ?, ?, ?, ?)',
        [distractionWhat, distractionWhen, distractionWhy, date, timestamp]
      );

      Alert.alert('Distraction Logged', 'Your distraction has been recorded.');
      setDistractionWhat('');
      setDistractionWhen('');
      setDistractionWhy('');
      setShowDistractionModal(false);
    } catch (error) {
      console.error('Error saving distraction:', error);
      Alert.alert('Error', 'Failed to save distraction entry');
    }
  };

  const shuffleAffirmation = async () => {
    if (affirmations.length === 0) return;
    
    const randomIndex = Math.floor(Math.random() * affirmations.length);
    const selectedAffirmation = affirmations[randomIndex];
    setCurrentAffirmation(selectedAffirmation.text);

    // Update usage count
    try {
      const db = dbManager.getDatabase();
      await db.runAsync(
        'UPDATE affirmations SET times_used = times_used + 1 WHERE id = ?',
        [selectedAffirmation.id]
      );
      await loadAffirmations();
    } catch (error) {
      console.error('Error updating affirmation usage:', error);
    }
  };

  const addCustomAffirmation = async () => {
    if (!customAffirmation.trim()) {
      Alert.alert('Empty Affirmation', 'Please enter an affirmation.');
      return;
    }

    try {
      const db = dbManager.getDatabase();
      const createdAt = new Date().toISOString();

      await db.runAsync(
        'INSERT INTO affirmations (text, is_custom, created_at) VALUES (?, ?, ?)',
        [customAffirmation, true, createdAt]
      );

      Alert.alert('Affirmation Added', 'Your custom affirmation has been saved.');
      setCustomAffirmation('');
      await loadAffirmations();
    } catch (error) {
      console.error('Error adding affirmation:', error);
      Alert.alert('Error', 'Failed to add affirmation');
    }
  };

  const saveEmotionReflection = async () => {
    if (!selectedEmotion) {
      Alert.alert('No Emotion Selected', 'Please select an emotion first.');
      return;
    }

    try {
      const db = dbManager.getDatabase();
      const now = new Date();
      const date = now.toISOString().split('T')[0];
      const timestamp = now.toISOString();

      // Create a more detailed emotion string
      const fullEmotion = selectedSecondaryEmotion
        ? `${selectedEmotion} - ${selectedSecondaryEmotion}`
        : selectedEmotion;

      const intensityText = emotionIntensity ? ` (${emotionIntensity} intensity)` : '';
      const emotionWithIntensity = fullEmotion + intensityText;

      await db.runAsync(
        'INSERT INTO emotion_reflections (primary_emotion, secondary_emotion, reflection, date, timestamp) VALUES (?, ?, ?, ?, ?)',
        [emotionWithIntensity, selectedSecondaryEmotion || null, emotionReflection, date, timestamp]
      );

      Alert.alert('Reflection Saved', 'Your emotion reflection has been recorded.');
      setSelectedEmotion('');
      setSelectedSecondaryEmotion('');
      setEmotionIntensity('');
      setEmotionReflection('');
      setShowEmotionWheel(false);
    } catch (error) {
      console.error('Error saving emotion reflection:', error);
      Alert.alert('Error', 'Failed to save emotion reflection');
    }
  };


  const callContact = async (phoneNumber: string) => {
    try {
      const url = `tel:${phoneNumber}`;
      const supported = await Linking.canOpenURL(url);
      
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert('Error', 'Phone calls are not supported on this device');
      }
    } catch (error) {
      console.error('Error making phone call:', error);
      Alert.alert('Error', 'Failed to make phone call');
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">More Tools</ThemedText>
        <ThemedText style={styles.subtitle}>Additional wellness resources</ThemedText>
      </ThemedView>

      {/* Tool Cards */}
      <ThemedView style={styles.toolsGrid}>
        <TouchableOpacity
          style={[styles.toolCard, { borderColor: tintColor }]}
          onPress={() => setShowDistractionModal(true)}
        >
          <IconSymbol size={32} name="exclamationmark.triangle" color={tintColor} />
          <ThemedText style={styles.toolTitle}>Distraction Log</ThemedText>
          <ThemedText style={styles.toolDescription}>Track what pulls your attention</ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.toolCard, { borderColor: tintColor }]}
          onPress={() => setShowAffirmations(true)}
        >
          <IconSymbol size={32} name="heart" color={tintColor} />
          <ThemedText style={styles.toolTitle}>Affirmations</ThemedText>
          <ThemedText style={styles.toolDescription}>Positive self-talk reminders</ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.toolCard, { borderColor: tintColor }]}
          onPress={() => setShowEmotionWheel(true)}
        >
          <IconSymbol size={32} name="circle.grid.hex" color={tintColor} />
          <ThemedText style={styles.toolTitle}>Emotion Wheel</ThemedText>
          <ThemedText style={styles.toolDescription}>Identify and reflect on feelings</ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.toolCard, { borderColor: tintColor }]}
          onPress={() => setShowSafetyPlan(true)}
        >
          <IconSymbol size={32} name="shield.checkered" color={tintColor} />
          <ThemedText style={styles.toolTitle}>Safety Plan</ThemedText>
          <ThemedText style={styles.toolDescription}>Crisis support and coping strategies</ThemedText>
        </TouchableOpacity>
      </ThemedView>

      {/* Distraction Modal */}
      <Modal visible={showDistractionModal} animationType="slide" presentationStyle="pageSheet">
        <ThemedView style={[styles.modalContainer, { backgroundColor }]}>
          <ThemedView style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowDistractionModal(false)}>
              <ThemedText style={[styles.cancelButton, { color: tintColor }]}>Cancel</ThemedText>
            </TouchableOpacity>
            <ThemedText type="subtitle">Log Distraction</ThemedText>
            <TouchableOpacity onPress={addDistraction}>
              <ThemedText style={[styles.saveButton, { color: tintColor }]}>Save</ThemedText>
            </TouchableOpacity>
          </ThemedView>

          <ScrollView style={styles.modalContent}>
            <ThemedView style={styles.inputSection}>
              <ThemedText style={styles.inputLabel}>What distracted you?</ThemedText>
              <TextInput
                style={[styles.textInput, { color: textColor, borderColor: tintColor }]}
                placeholder="e.g., Social media notification"
                placeholderTextColor={textColor + '80'}
                value={distractionWhat}
                onChangeText={setDistractionWhat}
                multiline
              />
            </ThemedView>

            <ThemedView style={styles.inputSection}>
              <ThemedText style={styles.inputLabel}>When did it happen?</ThemedText>
              <TextInput
                style={[styles.textInput, { color: textColor, borderColor: tintColor }]}
                placeholder="e.g., During work meeting"
                placeholderTextColor={textColor + '80'}
                value={distractionWhen}
                onChangeText={setDistractionWhen}
              />
            </ThemedView>

            <ThemedView style={styles.inputSection}>
              <ThemedText style={styles.inputLabel}>Why do you think it happened?</ThemedText>
              <TextInput
                style={[styles.textInput, { color: textColor, borderColor: tintColor }]}
                placeholder="e.g., I was feeling bored"
                placeholderTextColor={textColor + '80'}
                value={distractionWhy}
                onChangeText={setDistractionWhy}
                multiline
              />
            </ThemedView>

            <ThemedView style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.historyLink, { borderColor: tintColor }]}
                onPress={() => {
                  setShowDistractionModal(false);
                  router.push('/distraction-history');
                }}
              >
                <IconSymbol size={16} name="clock" color={tintColor} />
                <ThemedText style={[styles.historyLinkText, { color: tintColor }]}>
                  View History
                </ThemedText>
              </TouchableOpacity>
            </ThemedView>
          </ScrollView>
        </ThemedView>
      </Modal>

      {/* Affirmations Modal */}
      <Modal visible={showAffirmations} animationType="slide" presentationStyle="pageSheet">
        <ThemedView style={[styles.modalContainer, { backgroundColor }]}>
          <ThemedView style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowAffirmations(false)}>
              <ThemedText style={[styles.cancelButton, { color: tintColor }]}>Close</ThemedText>
            </TouchableOpacity>
            <ThemedText type="subtitle">Affirmations</ThemedText>
            <ThemedView style={{ width: 60 }} />
          </ThemedView>
          
          <ScrollView style={styles.modalContent}>
            <ThemedView style={styles.affirmationDisplay}>
              <ThemedText style={styles.affirmationText}>
                {currentAffirmation || "Tap shuffle to get an affirmation"}
              </ThemedText>
              
              <TouchableOpacity
                style={[styles.shuffleButton, { backgroundColor: tintColor }]}
                onPress={shuffleAffirmation}
              >
                <IconSymbol size={20} name="shuffle" color="#FAF9F6" />
                <ThemedText style={styles.shuffleButtonText}>Shuffle</ThemedText>
              </TouchableOpacity>
            </ThemedView>
            
            <ThemedView style={styles.inputSection}>
              <ThemedText style={styles.inputLabel}>Add Your Own Affirmation</ThemedText>
              <TextInput
                style={[styles.textInput, { color: textColor, borderColor: tintColor }]}
                placeholder="Write a positive affirmation..."
                placeholderTextColor={textColor + '80'}
                value={customAffirmation}
                onChangeText={setCustomAffirmation}
                multiline
              />
              <TouchableOpacity
                style={[styles.addButton, { backgroundColor: tintColor }]}
                onPress={addCustomAffirmation}
              >
                <ThemedText style={styles.addButtonText}>Add Affirmation</ThemedText>
              </TouchableOpacity>
            </ThemedView>
          </ScrollView>
        </ThemedView>
      </Modal>

      {/* Emotion Wheel Modal */}
      <Modal visible={showEmotionWheel} animationType="slide" presentationStyle="pageSheet">
        <ThemedView style={[styles.modalContainer, { backgroundColor }]}>
          <ThemedView style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowEmotionWheel(false)}>
              <ThemedText style={[styles.cancelButton, { color: tintColor }]}>Cancel</ThemedText>
            </TouchableOpacity>
            <ThemedText type="subtitle">Emotion Wheel</ThemedText>
            <TouchableOpacity onPress={saveEmotionReflection}>
              <ThemedText style={[styles.saveButton, { color: tintColor }]}>Save</ThemedText>
            </TouchableOpacity>
          </ThemedView>
          
          <ScrollView style={styles.modalContent}>
            <ThemedText style={styles.instructionText}>
              First, select a primary emotion, then choose a more specific feeling:
            </ThemedText>
            
            <ThemedView style={styles.emotionsContainer}>
              {primaryEmotions.map((emotion) => {
                const isSelected = selectedEmotion === emotion;
                const emotionColor = emotionColors[emotion] || tintColor;
                return (
                  <TouchableOpacity
                    key={emotion}
                    style={[
                      styles.primaryEmotionButton,
                      { borderColor: emotionColor, borderWidth: 2 },
                      isSelected && { backgroundColor: emotionColor }
                    ]}
                    onPress={() => {
                      setSelectedEmotion(emotion);
                      setSelectedSecondaryEmotion(''); // Reset secondary emotion when primary changes
                    }}
                  >
                    <ThemedText style={[
                      styles.primaryEmotionText,
                      isSelected && { color: 'white' }
                    ]}>
                      {emotion}
                    </ThemedText>
                  </TouchableOpacity>
                );
              })}
            </ThemedView>
            
            {selectedEmotion && emotionDetails[selectedEmotion as keyof typeof emotionDetails] && (
              <ThemedView style={styles.detailEmotionsContainer}>
                <ThemedText style={styles.detailEmotionsTitle}>
                  More specific {selectedEmotion.toLowerCase()} feelings:
                </ThemedText>
                <ThemedView style={styles.detailEmotionsGrid}>
                  {emotionDetails[selectedEmotion as keyof typeof emotionDetails].map((detail) => (
                    <TouchableOpacity
                      key={detail}
                      style={[
                        styles.detailEmotionButton,
                        selectedSecondaryEmotion === detail && { backgroundColor: tintColor }
                      ]}
                      onPress={() => setSelectedSecondaryEmotion(detail)}
                    >
                      <ThemedText style={[
                        styles.detailEmotionText,
                        selectedSecondaryEmotion === detail && { color: 'white' }
                      ]}>
                        {detail}
                      </ThemedText>
                    </TouchableOpacity>
                  ))}
                </ThemedView>
              </ThemedView>
            )}

            {selectedEmotion && (
              <ThemedView style={styles.intensityContainer}>
                <ThemedText style={styles.intensityTitle}>How intense is this feeling?</ThemedText>
                <ThemedView style={styles.intensityButtons}>
                  {[
                    { key: 'low', label: 'Low', description: 'Mild, subtle' },
                    { key: 'medium', label: 'Medium', description: 'Noticeable, clear' },
                    { key: 'high', label: 'High', description: 'Strong, overwhelming' }
                  ].map((intensity) => (
                    <TouchableOpacity
                      key={intensity.key}
                      style={[
                        styles.intensityButton,
                        { borderColor: tintColor },
                        emotionIntensity === intensity.key && { backgroundColor: tintColor }
                      ]}
                      onPress={() => setEmotionIntensity(intensity.key as any)}
                    >
                      <ThemedText style={[
                        styles.intensityLabel,
                        emotionIntensity === intensity.key && { color: 'white' }
                      ]}>
                        {intensity.label}
                      </ThemedText>
                      <ThemedText style={[
                        styles.intensityDescription,
                        emotionIntensity === intensity.key && { color: 'white' }
                      ]}>
                        {intensity.description}
                      </ThemedText>
                    </TouchableOpacity>
                  ))}
                </ThemedView>
              </ThemedView>
            )}
            
            {selectedEmotion && (
              <ThemedView style={styles.inputSection}>
                <ThemedText style={styles.inputLabel}>Reflect on this emotion:</ThemedText>
                <TextInput
                  style={[styles.textInput, { color: textColor, borderColor: tintColor }]}
                  placeholder="What triggered this feeling? How can you respond to it?"
                  placeholderTextColor={textColor + '80'}
                  value={emotionReflection}
                  onChangeText={setEmotionReflection}
                  multiline
                  numberOfLines={4}
                />
              </ThemedView>
            )}

            <ThemedView style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.historyLink, { borderColor: tintColor }]}
                onPress={() => {
                  setShowEmotionWheel(false);
                  router.push('/emotion-history');
                }}
              >
                <IconSymbol size={16} name="clock" color={tintColor} />
                <ThemedText style={[styles.historyLinkText, { color: tintColor }]}>
                  View Emotion History
                </ThemedText>
              </TouchableOpacity>
            </ThemedView>
          </ScrollView>
        </ThemedView>
      </Modal>

      {/* Safety Plan Modal */}
      <Modal visible={showSafetyPlan} animationType="slide" presentationStyle="pageSheet">
        <ThemedView style={[styles.modalContainer, { backgroundColor }]}>
          <ThemedView style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowSafetyPlan(false)}>
              <ThemedText style={[styles.cancelButton, { color: tintColor }]}>Close</ThemedText>
            </TouchableOpacity>
            <ThemedText type="subtitle">Safety Plan</ThemedText>
            <ThemedView style={{ width: 60 }} />
          </ThemedView>
          
          <ScrollView style={styles.modalContent}>
            <ThemedView style={styles.safetySection}>
              <ThemedText type="subtitle" style={styles.safetySectionTitle}>
                Calming Strategies
              </ThemedText>
              {calmingStrategies.map((strategy, index) => (
                <ThemedView key={index} style={styles.strategyItem}>
                  <ThemedText style={styles.strategyText}>• {strategy}</ThemedText>
                </ThemedView>
              ))}
            </ThemedView>
            
            <ThemedView style={styles.safetySection}>
              <ThemedText type="subtitle" style={styles.safetySectionTitle}>
                Emergency Contacts
              </ThemedText>
              {emergencyContacts.map((contact) => (
                <TouchableOpacity
                  key={contact.id}
                  style={styles.contactItem}
                  onPress={() => callContact(contact.phone)}
                >
                  <ThemedView style={styles.contactInfo}>
                    <ThemedText style={styles.contactName}>{contact.name}</ThemedText>
                    <ThemedText style={styles.contactPhone}>{contact.phone}</ThemedText>
                  </ThemedView>
                  <IconSymbol size={20} name="phone.fill" color={tintColor} />
                </TouchableOpacity>
              ))}
            </ThemedView>
          </ScrollView>
        </ThemedView>
      </Modal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
    marginTop: 4,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 20,
    justifyContent: 'space-between',
    backgroundColor: 'transparent',
  },
  toolsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 20,
    justifyContent: 'space-between',
    backgroundColor: 'transparent',
  },
  toolCard: {
    width: '48%',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderWidth: 1,
  },
  toolTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
  toolDescription: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 4,
    textAlign: 'center',
  },
  featureCard: {
    width: '48%',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
  featureSubtitle: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 4,
    textAlign: 'center',
  },
  settingsSection: {
    padding: 20,
    backgroundColor: 'transparent',
  },
  sectionTitle: {
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  settingText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
  },
  modalContainer: {
    flex: 1,
    paddingTop: 60,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  cancelButton: {
    fontSize: 16,
  },
  saveButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  inputSection: {
    marginBottom: 24,
    backgroundColor: 'transparent',
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 50,
  },
  affirmationDisplay: {
    padding: 20,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
    alignItems: 'center',
    marginBottom: 24,
  },
  affirmationText: {
    fontSize: 18,
    textAlign: 'center',
    lineHeight: 26,
    marginBottom: 20,
  },
  shuffleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  shuffleButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  addButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 12,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  instructionText: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 20,
    textAlign: 'center',
  },
  emotionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20,
    backgroundColor: 'transparent',
  },
  primaryEmotionButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.05)',
    marginRight: 8,
    marginBottom: 8,
  },
  primaryEmotionText: {
    fontSize: 16,
    fontWeight: '600',
  },
  detailEmotionsContainer: {
    marginBottom: 20,
    backgroundColor: 'transparent',
  },
  detailEmotionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  detailEmotionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: 'transparent',
  },
  detailEmotionButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.05)',
    marginRight: 8,
    marginBottom: 8,
  },
  detailEmotionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  safetySection: {
    marginBottom: 24,
    backgroundColor: 'transparent',
  },
  safetySectionTitle: {
    marginBottom: 16,
  },
  strategyItem: {
    paddingVertical: 8,
    backgroundColor: 'transparent',
  },
  strategyText: {
    fontSize: 16,
    lineHeight: 24,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  contactInfo: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
  },
  contactPhone: {
    fontSize: 14,
    opacity: 0.7,
    marginTop: 2,
  },
  modalFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
    alignItems: 'center',
  },
  historyLink: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderRadius: 8,
    gap: 8,
  },
  historyLinkText: {
    fontSize: 14,
    fontWeight: '500',
  },
  intensityContainer: {
    marginBottom: 20,
    backgroundColor: 'transparent',
  },
  intensityTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  intensityButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  intensityButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  intensityLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  intensityDescription: {
    fontSize: 12,
    opacity: 0.7,
    textAlign: 'center',
  },
});
