import { useEffect, useRef, useState } from 'react';
import { Animated, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';

type BreathingType = 'box' | '478' | 'custom';
type ExerciseType = 'breathing' | 'bodyscan' | 'relaxation';

export default function BreatheScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = useThemeColor({}, 'tint');
  
  const [activeExercise, setActiveExercise] = useState<ExerciseType>('breathing');
  const [breathingType, setBreathingType] = useState<BreathingType>('box');
  const [isActive, setIsActive] = useState(false);
  const [phase, setPhase] = useState<'inhale' | 'hold1' | 'exhale' | 'hold2'>('inhale');
  const [timer, setTimer] = useState(0);
  const [customDuration, setCustomDuration] = useState(5);
  
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [activeDot, setActiveDot] = useState(0); // Track which dot is currently active (0-15 for 4 dots per side)

  const breathingPatterns = {
    box: { inhale: 4, hold1: 4, exhale: 4, hold2: 4 },
    '478': { inhale: 4, hold1: 7, exhale: 8, hold2: 0 },
    custom: { inhale: customDuration, hold1: 0, exhale: customDuration, hold2: 0 }
  };

  const bodyParts = [
    'Head and Face', 'Neck and Shoulders', 'Arms and Hands', 'Chest',
    'Upper Back', 'Abdomen', 'Lower Back', 'Hips', 'Thighs', 'Calves and Feet'
  ];

  const relaxationSteps = [
    'Find a comfortable position and close your eyes',
    'Start with your toes - tense them for 5 seconds, then release',
    'Move to your calves - tense and release',
    'Tense your thigh muscles, hold, then let go',
    'Clench your fists, hold, then release',
    'Tense your arms and shoulders, then relax',
    'Scrunch your face muscles, then let them go',
    'Take three deep breaths and enjoy the relaxation'
  ];

  const [currentStep, setCurrentStep] = useState(0);
  const [selectedBodyParts, setSelectedBodyParts] = useState<string[]>([]);

  useEffect(() => {
    if (isActive) {
      const pattern = breathingPatterns[breathingType];
      const phases: Array<keyof typeof pattern> = ['inhale', 'hold1', 'exhale', 'hold2'];
      let currentPhaseIndex = 0;
      let phaseTimer = 0;

      intervalRef.current = setInterval(() => {
        const currentPhaseDuration = pattern[phases[currentPhaseIndex]];
        
        if (currentPhaseDuration === 0) {
          currentPhaseIndex = (currentPhaseIndex + 1) % phases.length;
          phaseTimer = 0;
          return;
        }

        phaseTimer++;
        setTimer(phaseTimer);

        // Update active dot for box breathing (16 dots total, 4 per side)
        if (breathingType === 'box') {
          const totalDots = 16;
          const dotsPerPhase = 4;
          const phaseStartDot = currentPhaseIndex * dotsPerPhase;
          const dotInPhase = (phaseTimer - 1) % dotsPerPhase;
          setActiveDot(phaseStartDot + dotInPhase);
        } else {
          // Animation for breathing circle (non-box patterns)
          if (phases[currentPhaseIndex] === 'inhale') {
            Animated.timing(scaleAnim, {
              toValue: 1.3,
              duration: currentPhaseDuration * 1000,
              useNativeDriver: true,
            }).start();
          } else if (phases[currentPhaseIndex] === 'exhale') {
            Animated.timing(scaleAnim, {
              toValue: 1,
              duration: currentPhaseDuration * 1000,
              useNativeDriver: true,
            }).start();
          }
        }

        if (phaseTimer >= currentPhaseDuration) {
          currentPhaseIndex = (currentPhaseIndex + 1) % phases.length;
          phaseTimer = 0;
          setPhase(phases[currentPhaseIndex] as any);
        }
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      scaleAnim.setValue(1);
      setActiveDot(0);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isActive, breathingType, customDuration]);

  const startBreathing = () => {
    setIsActive(true);
    setPhase('inhale');
    setTimer(0);
  };

  const stopBreathing = () => {
    setIsActive(false);
    setTimer(0);
    setActiveDot(0);
  };

  const toggleBodyPart = (part: string) => {
    setSelectedBodyParts(prev => 
      prev.includes(part) 
        ? prev.filter(p => p !== part)
        : [...prev, part]
    );
  };

  const getPhaseInstruction = () => {
    const pattern = breathingPatterns[breathingType];
    const duration = pattern[phase];
    
    switch (phase) {
      case 'inhale':
        return `Breathe In (${duration}s)`;
      case 'hold1':
        return `Hold (${duration}s)`;
      case 'exhale':
        return `Breathe Out (${duration}s)`;
      case 'hold2':
        return `Hold (${duration}s)`;
      default:
        return 'Breathe';
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">Breathe & Relax</ThemedText>
        <ThemedText style={styles.subtitle}>Find your calm</ThemedText>
      </ThemedView>
      

      {/* Exercise Type Selector */}
      <ThemedView style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeExercise === 'breathing' && { backgroundColor: tintColor }
          ]}
          onPress={() => setActiveExercise('breathing')}
        >
          <ThemedText style={[
            styles.tabText,
            activeExercise === 'breathing' && { color: 'white' }
          ]}>
            Breathing
          </ThemedText>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tab,
            activeExercise === 'bodyscan' && { backgroundColor: tintColor }
          ]}
          onPress={() => setActiveExercise('bodyscan')}
        >
          <ThemedText style={[
            styles.tabText,
            activeExercise === 'bodyscan' && { color: 'white' }
          ]}>
            Body Scan
          </ThemedText>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tab,
            activeExercise === 'relaxation' && { backgroundColor: tintColor }
          ]}
          onPress={() => setActiveExercise('relaxation')}
        >
          <ThemedText style={[
            styles.tabText,
            activeExercise === 'relaxation' && { color: 'white' }
          ]}>
            Relaxation
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>

      {/* Breathing Exercises */}
      {activeExercise === 'breathing' && (
        <ThemedView style={styles.breathingSection}>
          {/* Breathing Pattern Selector */}
          <ThemedView style={styles.patternSelector}>
            <TouchableOpacity
              style={[
                styles.patternButton,
                breathingType === 'box' && { backgroundColor: tintColor }
              ]}
              onPress={() => setBreathingType('box')}
            >
              <ThemedText style={[
                styles.patternText,
                breathingType === 'box' && { color: 'white' }
              ]}>
                Box (4-4-4-4)
              </ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.patternButton,
                breathingType === '478' && { backgroundColor: tintColor }
              ]}
              onPress={() => setBreathingType('478')}
            >
              <ThemedText style={[
                styles.patternText,
                breathingType === '478' && { color: 'white' }
              ]}>
                4-7-8
              </ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.patternButton,
                breathingType === 'custom' && { backgroundColor: tintColor }
              ]}
              onPress={() => setBreathingType('custom')}
            >
              <ThemedText style={[
                styles.patternText,
                breathingType === 'custom' && { color: 'white' }
              ]}>
                Custom
              </ThemedText>
            </TouchableOpacity>
          </ThemedView>

          {/* Breathing Visualization */}
          <ThemedView style={styles.breathingContainer}>
            {breathingType === 'box' ? (
              <ThemedView style={styles.boxContainer}>
                {/* Box with dots */}
                <ThemedView style={[styles.breathingBoxWithDots, { borderColor: tintColor }]}>
                  {/* Top side dots (0-3) */}
                  <ThemedView style={styles.topDots}>
                    {[0, 1, 2, 3].map((dotIndex) => (
                      <ThemedView
                        key={`top-${dotIndex}`}
                        style={[
                          styles.dot,
                          {
                            backgroundColor: isActive && activeDot === dotIndex ? tintColor : tintColor + '30'
                          }
                        ]}
                      />
                    ))}
                  </ThemedView>
                  
                  {/* Right side dots (4-7) */}
                  <ThemedView style={styles.rightDots}>
                    {[4, 5, 6, 7].map((dotIndex) => (
                      <ThemedView
                        key={`right-${dotIndex}`}
                        style={[
                          styles.dot,
                          {
                            backgroundColor: isActive && activeDot === dotIndex ? tintColor : tintColor + '30'
                          }
                        ]}
                      />
                    ))}
                  </ThemedView>
                  
                  {/* Bottom side dots (8-11) - right to left */}
                  <ThemedView style={styles.bottomDots}>
                    {[11, 10, 9, 8].map((dotIndex) => (
                      <ThemedView
                        key={`bottom-${dotIndex}`}
                        style={[
                          styles.dot,
                          {
                            backgroundColor: isActive && activeDot === dotIndex ? tintColor : tintColor + '30'
                          }
                        ]}
                      />
                    ))}
                  </ThemedView>
                  
                  {/* Left side dots (12-15) - bottom to top */}
                  <ThemedView style={styles.leftDots}>
                    {[15, 14, 13, 12].map((dotIndex) => (
                      <ThemedView
                        key={`left-${dotIndex}`}
                        style={[
                          styles.dot,
                          {
                            backgroundColor: isActive && activeDot === dotIndex ? tintColor : tintColor + '30'
                          }
                        ]}
                      />
                    ))}
                  </ThemedView>
                  
                  {/* Center content */}
                  <ThemedView style={styles.boxCenter}>
                    <ThemedText style={styles.breathingText}>
                      {isActive ? getPhaseInstruction() : 'Ready to breathe?'}
                    </ThemedText>
                    {isActive && (
                      <ThemedText style={styles.timerText}>
                        {timer}
                      </ThemedText>
                    )}
                  </ThemedView>
                </ThemedView>
              </ThemedView>
            ) : (
              <Animated.View
                style={[
                  styles.breathingCircle,
                  {
                    backgroundColor: tintColor + '30',
                    borderColor: tintColor,
                    transform: [{ scale: scaleAnim }]
                  }
                ]}
              >
                <ThemedText style={styles.breathingText}>
                  {isActive ? getPhaseInstruction() : 'Ready to breathe?'}
                </ThemedText>
                {isActive && (
                  <ThemedText style={styles.timerText}>
                    {timer}
                  </ThemedText>
                )}
              </Animated.View>
            )}
          </ThemedView>

          {/* Controls */}
          <ThemedView style={styles.controls}>
            {!isActive ? (
              <TouchableOpacity
                style={[styles.controlButton, { backgroundColor: tintColor }]}
                onPress={startBreathing}
              >
                <IconSymbol size={20} name="play.fill" color="#FAF9F6" />
                <ThemedText style={styles.controlButtonText}>Start</ThemedText>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={[styles.controlButton, { backgroundColor: '#ff4444' }]}
                onPress={stopBreathing}
              >
                <IconSymbol size={20} name="stop.fill" color="#FAF9F6" />
                <ThemedText style={styles.controlButtonText}>Stop</ThemedText>
              </TouchableOpacity>
            )}
          </ThemedView>
        </ThemedView>
      )}

      {/* Body Scan */}
      {activeExercise === 'bodyscan' && (
        <ThemedView style={styles.bodyScanSection}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Body Scan Meditation
          </ThemedText>
          <ThemedText style={styles.instructionText}>
            Tap on body parts as you focus on them during your meditation
          </ThemedText>
          
          <ThemedView style={styles.bodyPartsContainer}>
            {bodyParts.map((part) => (
              <TouchableOpacity
                key={part}
                style={[
                  styles.bodyPartButton,
                  selectedBodyParts.includes(part) && { backgroundColor: tintColor }
                ]}
                onPress={() => toggleBodyPart(part)}
              >
                <ThemedText style={[
                  styles.bodyPartText,
                  selectedBodyParts.includes(part) && { color: 'white' }
                ]}>
                  {part}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </ThemedView>
        </ThemedView>
      )}

      {/* Progressive Relaxation */}
      {activeExercise === 'relaxation' && (
        <ThemedView style={styles.relaxationSection}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Progressive Muscle Relaxation
          </ThemedText>
          
          <ThemedView style={styles.stepContainer}>
            <ThemedText style={styles.stepNumber}>
              Step {currentStep + 1} of {relaxationSteps.length}
            </ThemedText>
            <ThemedText style={styles.stepText}>
              {relaxationSteps[currentStep]}
            </ThemedText>
          </ThemedView>
          
          <ThemedView style={styles.relaxationControls}>
            <TouchableOpacity
              style={[styles.stepButton, { opacity: currentStep === 0 ? 0.5 : 1 }]}
              onPress={() => setCurrentStep(Math.max(0, currentStep - 1))}
              disabled={currentStep === 0}
            >
              <IconSymbol size={20} name="chevron.left" color={tintColor} />
              <ThemedText style={[styles.stepButtonText, { color: tintColor }]}>
                Previous
              </ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.stepButton, { opacity: currentStep === relaxationSteps.length - 1 ? 0.5 : 1 }]}
              onPress={() => setCurrentStep(Math.min(relaxationSteps.length - 1, currentStep + 1))}
              disabled={currentStep === relaxationSteps.length - 1}
            >
              <ThemedText style={[styles.stepButtonText, { color: tintColor }]}>
                Next
              </ThemedText>
              <IconSymbol size={20} name="chevron.right" color={tintColor} />
            </TouchableOpacity>
          </ThemedView>
        </ThemedView>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
    marginTop: 4,
  },
  tabContainer: {
    flexDirection: 'row',
    margin: 20,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 12,
    padding: 4,
  },
  tab: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  tabText: {
    fontWeight: '600',
    fontSize: 12,
  },
  breathingSection: {
    padding: 20,
    backgroundColor: 'transparent',
  },
  patternSelector: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 40,
    backgroundColor: 'transparent',
  },
  patternButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  patternText: {
    fontSize: 14,
    fontWeight: '500',
  },
  breathingContainer: {
    alignItems: 'center',
    marginBottom: 40,
    backgroundColor: 'transparent',
  },
  breathingCircle: {
    width: 200,
    height: 200,
    borderRadius: 100,
    borderWidth: 3,
    alignItems: 'center',
    justifyContent: 'center',
  },
  breathingBox: {
    width: 200,
    height: 200,
    borderRadius: 20,
    borderWidth: 3,
    alignItems: 'center',
    justifyContent: 'center',
  },
  boxContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  breathingBoxWithDots: {
    width: 200,
    height: 200,
    borderWidth: 2,
    borderRadius: 20,
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  boxCenter: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  dot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginHorizontal: 8,
  },
  topDots: {
    position: 'absolute',
    top: -12,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    paddingHorizontal: 30,
    zIndex: 10,
  },
  rightDots: {
    position: 'absolute',
    right: -12,
    top: 0,
    bottom: 0,
    flexDirection: 'column',
    justifyContent: 'space-evenly',
    paddingVertical: 30,
    zIndex: 10,
  },
  bottomDots: {
    position: 'absolute',
    bottom: -12,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    paddingHorizontal: 30,
    zIndex: 10,
  },
  leftDots: {
    position: 'absolute',
    left: -12,
    top: 0,
    bottom: 0,
    flexDirection: 'column',
    justifyContent: 'space-evenly',
    paddingVertical: 30,
    zIndex: 10,
  },
  breathingText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  timerText: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
  },
  controls: {
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  controlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  controlButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  bodyScanSection: {
    padding: 20,
    backgroundColor: 'transparent',
  },
  sectionTitle: {
    marginBottom: 16,
  },
  instructionText: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 20,
    textAlign: 'center',
  },
  bodyPartsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: 'transparent',
  },
  bodyPartButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.05)',
    marginRight: 8,
    marginBottom: 8,
  },
  bodyPartText: {
    fontSize: 14,
    fontWeight: '500',
  },
  relaxationSection: {
    padding: 20,
    backgroundColor: 'transparent',
  },
  stepContainer: {
    padding: 20,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
    marginBottom: 20,
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: '600',
    opacity: 0.7,
    marginBottom: 8,
  },
  stepText: {
    fontSize: 16,
    lineHeight: 24,
  },
  relaxationControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: 'transparent',
  },
  stepButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  stepButtonText: {
    fontSize: 16,
    fontWeight: '500',
    marginHorizontal: 8,
  },
});