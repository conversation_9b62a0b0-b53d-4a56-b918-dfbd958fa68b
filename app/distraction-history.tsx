import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { DatabaseManager } from '@/utils/database';

interface DistractionEntry {
  id: number;
  what: string;
  when_occurred: string;
  why: string;
  date: string;
  timestamp: string;
}

interface CalendarDay {
  date: string;
  count: number;
  entries: DistractionEntry[];
  isCurrentMonth: boolean;
}

export default function DistractionHistoryScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = useThemeColor({}, 'tint');
  const textColor = useThemeColor({}, 'text');
  const cardColor = useThemeColor({}, 'card');
  const borderColor = useThemeColor({}, 'border');
  
  const [entries, setEntries] = useState<DistractionEntry[]>([]);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [calendarData, setCalendarData] = useState<CalendarDay[]>([]);
  const [selectedDay, setSelectedDay] = useState<CalendarDay | null>(null);
  const [dbManager] = useState(() => DatabaseManager.getInstance());

  useEffect(() => {
    loadEntries();
  }, [currentDate]);

  const loadEntries = async () => {
    try {
      const db = dbManager.getDatabase();
      const result = await db.getAllAsync(
        'SELECT * FROM distraction_entries ORDER BY date DESC, timestamp DESC'
      );
      setEntries(result as DistractionEntry[]);
      generateCalendarData(result as DistractionEntry[]);
    } catch (error) {
      console.error('Error loading distraction entries:', error);
      Alert.alert('Error', 'Failed to load distraction entries');
    }
  };

  const generateCalendarData = (entries: DistractionEntry[]) => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const calendar: CalendarDay[] = [];
    const currentDateStr = startDate.toISOString().split('T')[0];
    
    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      const dateStr = date.toISOString().split('T')[0];
      
      const dayEntries = entries.filter(entry => entry.date === dateStr);
      
      calendar.push({
        date: dateStr,
        count: dayEntries.length,
        entries: dayEntries,
        isCurrentMonth: date.getMonth() === month
      });
    }
    
    setCalendarData(calendar);
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() + (direction === 'next' ? 1 : -1));
    setCurrentDate(newDate);
  };

  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getIntensityColor = (count: number) => {
    if (count === 0) return 'transparent';
    if (count === 1) return tintColor + '40';
    if (count <= 3) return tintColor + '70';
    return tintColor;
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <IconSymbol size={24} name="chevron.left" color={tintColor} />
        </TouchableOpacity>
        <ThemedText type="title">Distraction History</ThemedText>
        <TouchableOpacity onPress={() => router.push('/distraction-stats')}>
          <IconSymbol size={24} name="chart.line.uptrend.xyaxis" color={tintColor} />
        </TouchableOpacity>
      </ThemedView>

      {/* Calendar Header */}
      <ThemedView style={[styles.calendarHeader, { backgroundColor: cardColor }]}>
        <TouchableOpacity onPress={() => navigateMonth('prev')}>
          <IconSymbol size={20} name="chevron.left" color={tintColor} />
        </TouchableOpacity>
        <ThemedText type="subtitle">
          {currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
        </ThemedText>
        <TouchableOpacity onPress={() => navigateMonth('next')}>
          <IconSymbol size={20} name="chevron.right" color={tintColor} />
        </TouchableOpacity>
      </ThemedView>

      {/* Calendar Grid */}
      <ThemedView style={[styles.calendar, { backgroundColor: cardColor }]}>
        <ThemedView style={styles.weekHeader}>
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <ThemedText key={day} style={styles.weekDay}>{day}</ThemedText>
          ))}
        </ThemedView>
        
        <ThemedView style={styles.calendarGrid}>
          {calendarData.map((day, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.calendarDay,
                { backgroundColor: getIntensityColor(day.count) },
                !day.isCurrentMonth && styles.otherMonth,
                selectedDay?.date === day.date && { borderColor: tintColor, borderWidth: 2 }
              ]}
              onPress={() => setSelectedDay(day.count > 0 ? day : null)}
            >
              <ThemedText style={[
                styles.dayNumber,
                !day.isCurrentMonth && styles.otherMonthText,
                day.count > 2 && { color: 'white' }
              ]}>
                {new Date(day.date).getDate()}
              </ThemedText>
              {day.count > 0 && (
                <ThemedText style={[styles.countBadge, day.count > 2 && { color: 'white' }]}>
                  {day.count}
                </ThemedText>
              )}
            </TouchableOpacity>
          ))}
        </ThemedView>
      </ThemedView>

      {/* Selected Day Details */}
      {selectedDay && (
        <ThemedView style={[styles.dayDetails, { backgroundColor: cardColor }]}>
          <ThemedText type="subtitle" style={styles.dayDetailsTitle}>
            {formatDate(selectedDay.date)} - {selectedDay.count} distraction{selectedDay.count !== 1 ? 's' : ''}
          </ThemedText>
          
          {selectedDay.entries.map((entry, index) => (
            <ThemedView key={entry.id} style={[styles.entryCard, { borderColor }]}>
              <ThemedText style={styles.entryWhat}>{entry.what}</ThemedText>
              {entry.when_occurred && (
                <ThemedText style={styles.entryWhen}>When: {entry.when_occurred}</ThemedText>
              )}
              {entry.why && (
                <ThemedText style={styles.entryWhy}>Why: {entry.why}</ThemedText>
              )}
              <ThemedText style={styles.entryTime}>
                {new Date(entry.timestamp).toLocaleTimeString('en-US', {
                  hour: 'numeric',
                  minute: '2-digit'
                })}
              </ThemedText>
            </ThemedView>
          ))}
        </ThemedView>
      )}

      {entries.length === 0 && (
        <ThemedView style={[styles.emptyState, { backgroundColor: cardColor }]}>
          <IconSymbol size={48} name="exclamationmark.triangle" color={tintColor} />
          <ThemedText style={styles.emptyTitle}>No distractions logged</ThemedText>
          <ThemedText style={styles.emptySubtitle}>
            Start tracking your distractions to see patterns
          </ThemedText>
        </ThemedView>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  calendarHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    marginHorizontal: 20,
    borderRadius: 12,
    marginBottom: 16,
  },
  calendar: {
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  weekHeader: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  weekDay: {
    flex: 1,
    textAlign: 'center',
    fontSize: 12,
    fontWeight: '600',
    opacity: 0.7,
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  calendarDay: {
    width: '14.28%',
    aspectRatio: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    margin: 1,
    position: 'relative',
  },
  otherMonth: {
    opacity: 0.3,
  },
  dayNumber: {
    fontSize: 14,
    fontWeight: '500',
  },
  otherMonthText: {
    opacity: 0.5,
  },
  countBadge: {
    fontSize: 10,
    fontWeight: 'bold',
    position: 'absolute',
    bottom: 2,
  },
  dayDetails: {
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  dayDetailsTitle: {
    marginBottom: 16,
  },
  entryCard: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  entryWhat: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  entryWhen: {
    fontSize: 14,
    opacity: 0.8,
    marginBottom: 2,
  },
  entryWhy: {
    fontSize: 14,
    opacity: 0.8,
    marginBottom: 4,
  },
  entryTime: {
    fontSize: 12,
    opacity: 0.6,
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
    marginHorizontal: 20,
    borderRadius: 12,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
  },
});