import React, { useEffect, useState } from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Spacing } from '@/constants/Spacing';
import { DatabaseManager } from '@/utils/database';

const { width } = Dimensions.get('window');

export default function DashboardScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = useThemeColor({}, 'tint');
  const accentColor = useThemeColor({}, 'accent');
  const successColor = useThemeColor({}, 'success');
  const warningColor = useThemeColor({}, 'warning');
  
  const [moodData, setMoodData] = useState<any[]>([]);
  const [habitData, setHabitData] = useState<any[]>([]);
  const [journalData, setJournalData] = useState<any[]>([]);
  const [dbManager] = useState(() => DatabaseManager.getInstance());

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      await dbManager.initDatabase();
      const db = dbManager.getDatabase();
      
      // Debug: Log database tables
      console.log('Loading dashboard data...');
      
      // Load mood data with more robust error handling
      try {
        console.log('Checking for mood tables...');
        // Get all tables in the database
        const allTables = await db.getAllAsync(
          "SELECT name FROM sqlite_master WHERE type='table'"
        );
        console.log('Available tables:', allTables.map(t => t.name).join(', '));
        
        // We'll try to find mood data regardless of table structure
        let moods = [];
        
        // Try different table names that might exist
        const potentialTables = ['mood_logs', 'moods', 'mood_entries', 'mood_assessments', 'mood_data'];
        
        for (const tableName of potentialTables) {
          try {
            // Check if this table exists
            const tableExists = await db.getAllAsync(
              `SELECT name FROM sqlite_master WHERE type='table' AND name='${tableName}'`
            );
            
            if (tableExists && tableExists.length > 0) {
              console.log(`Found table: ${tableName}, attempting to query...`);
              
              // Try different sort columns that might exist
              const sortColumns = ['created_at', 'date', 'timestamp', 'created', 'time'];
              
              for (const sortCol of sortColumns) {
                try {
                  const query = `SELECT * FROM ${tableName} ORDER BY ${sortCol} DESC LIMIT 7`;
                  console.log(`Trying query: ${query}`);
                  
                  const result = await db.getAllAsync(query);
                  if (result && result.length > 0) {
                    console.log(`Success! Found ${result.length} entries in ${tableName} sorted by ${sortCol}`);
                    moods = result;
                    break;
                  }
                } catch (sortErr) {
                  // This sort column doesn't exist, try the next one
                }
              }
              
              // If we found data, no need to check other tables
              if (moods.length > 0) {
                break;
              }
              
              // If no sort column worked, try without sorting
              if (moods.length === 0) {
                try {
                  const result = await db.getAllAsync(`SELECT * FROM ${tableName} LIMIT 7`);
                  if (result && result.length > 0) {
                    console.log(`Success! Found ${result.length} entries in ${tableName} (unsorted)`);
                    moods = result;
                    break;
                  }
                } catch (err) {
                  console.log(`Failed to query ${tableName} without sorting`);
                }
              }
            }
          } catch (err) {
            console.log(`Error checking table ${tableName}:`, err);
          }
        }
        
        // Log the actual mood data for debugging
        if (moods.length > 0) {
          console.log('First mood entry sample:', JSON.stringify(moods[0]));
        } else {
          console.log('No mood data found in any of the standard tables');
        }
        // If we still have no mood data, try one more approach - look for any table with mood-related columns
        if (moods.length === 0) {
          console.log('Trying to find any table with mood data...');
          
          // Get all tables
          const tables = await db.getAllAsync(
            "SELECT name FROM sqlite_master WHERE type='table'"
          );
          
          // Try each table
          for (const table of tables) {
            try {
              // Get table info
              const tableInfo = await db.getAllAsync(`PRAGMA table_info(${table.name})`);
              const columnNames = tableInfo.map(col => col.name);
              
              // Check if this looks like a table that might contain mood data
              const moodRelatedColumns = ['mood', 'emotion', 'feeling', 'pleasantness', 'intensity'];
              const hasMoodColumns = moodRelatedColumns.some(col => 
                columnNames.some(tableCol => tableCol.toLowerCase().includes(col.toLowerCase()))
              );
              
              if (hasMoodColumns) {
                console.log(`Found potential mood data in table: ${table.name}`);
                moods = await db.getAllAsync(`SELECT * FROM ${table.name} LIMIT 7`);
                if (moods.length > 0) {
                  console.log(`Found ${moods.length} mood entries in ${table.name}`);
                  break;
                }
              }
            } catch (err) {
              console.log(`Error checking table ${table.name}:`, err);
            }
          }
        }
        
        setMoodData(moods as any[]);
      } catch (moodError) {
        console.error('Error loading mood data:', moodError);
        setMoodData([]);
      }

      // Load habit data with more robust error handling
      try {
        console.log('Checking for habits table...');
        // First check if the table exists
        const tableCheck = await db.getAllAsync(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='habits'"
        );
        
        if (tableCheck && tableCheck.length > 0) {
          console.log('habits table exists, fetching data...');
          const today = new Date().toISOString().split('T')[0];
          
          // First get all habits
          const habits = await db.getAllAsync(
            'SELECT * FROM habits ORDER BY created_at DESC'
          );
          
          // Then check for completions separately to avoid join issues
          const habitsWithCompletions = await Promise.all(habits.map(async (habit) => {
            try {
              const completions = await db.getAllAsync(
                'SELECT * FROM habit_completions WHERE habit_id = ? AND DATE(completed_at) = ?',
                [habit.id, today]
              );
              return {
                ...habit,
                completed_at: completions.length > 0 ? completions[0].completed_at : null
              };
            } catch (err) {
              console.log('No completions for habit:', habit.id);
              return {
                ...habit,
                completed_at: null
              };
            }
          }));
          
          console.log('Habit data loaded:', habitsWithCompletions.length, 'entries');
          setHabitData(habitsWithCompletions as any[]);
        } else {
          console.log('habits table does not exist');
          setHabitData([]);
        }
      } catch (habitError) {
        console.error('Error loading habit data:', habitError);
        setHabitData([]);
      }

      // Load journal data with more robust error handling
      try {
        console.log('Checking for journal_entries table...');
        // First check if the table exists
        const tableCheck = await db.getAllAsync(
          "SELECT name FROM sqlite_master WHERE type='table' AND name='journal_entries'"
        );
        
        if (tableCheck && tableCheck.length > 0) {
          console.log('journal_entries table exists, fetching data...');
          
          // Try different sort columns that might exist
          const sortColumns = ['timestamp', 'created_at', 'date', 'id'];
          let journals = [];
          
          for (const sortCol of sortColumns) {
            try {
              const query = `SELECT * FROM journal_entries ORDER BY ${sortCol} DESC LIMIT 3`;
              console.log(`Trying journal query: ${query}`);
              
              const result = await db.getAllAsync(query);
              if (result && result.length > 0) {
                console.log(`Success! Found ${result.length} journal entries sorted by ${sortCol}`);
                journals = result;
                break;
              }
            } catch (sortErr) {
              console.log(`Failed to sort by ${sortCol}:`, sortErr);
              // This sort column doesn't exist, try the next one
            }
          }
          
          // If no sorting worked, try without sorting
          if (journals.length === 0) {
            try {
              journals = await db.getAllAsync('SELECT * FROM journal_entries LIMIT 3');
              console.log(`Found ${journals.length} journal entries without sorting`);
            } catch (err) {
              console.log('Failed to get journal entries without sorting:', err);
            }
          }
          
          console.log('Journal data loaded:', journals.length, 'entries');
          if (journals.length > 0) {
            console.log('First journal entry sample:', JSON.stringify(journals[0]));
          }
          setJournalData(journals as any[]);
        } else {
          console.log('journal_entries table does not exist');
          setJournalData([]);
        }
      } catch (journalError) {
        console.error('Error loading journal data:', journalError);
        setJournalData([]);
      }
    } catch (error) {
      console.error('Failed to initialize database:', error);
      // Set empty arrays as fallback
      setMoodData([]);
      setHabitData([]);
      setJournalData([]);
    }
  };

  const getMoodTrend = () => {
    if (moodData.length < 2) return 'neutral';
    
    // Handle different possible data structures
    const getMoodValue = (mood: any) => {
      // Check different possible field names for pleasantness
      if (mood.pleasantness === 'Pleasant') return 1;
      if (mood.pleasantness === 'Unpleasant') return -1;
      
      // If we have a mood field instead
      if (mood.mood) {
        const moodLower = mood.mood.toLowerCase();
        if (moodLower.includes('good') || 
            moodLower.includes('happy') || 
            moodLower.includes('pleasant') ||
            moodLower.includes('joy') ||
            moodLower.includes('calm') ||
            moodLower.includes('content')) return 1;
            
        if (moodLower.includes('bad') || 
            moodLower.includes('sad') || 
            moodLower.includes('unpleasant') ||
            moodLower.includes('angry') ||
            moodLower.includes('anxious') ||
            moodLower.includes('stress')) return -1;
      }
      
      // Check emotion field
      if (mood.emotion) {
        const emotionLower = mood.emotion.toLowerCase();
        if (emotionLower.includes('joy') || 
            emotionLower.includes('happy') || 
            emotionLower.includes('calm')) return 1;
        if (emotionLower.includes('sad') || 
            emotionLower.includes('fear') || 
            emotionLower.includes('anger')) return -1;
      }
      
      // Default case
      return 0;
    };
    
    const recent = moodData.slice(0, 3);
    const older = moodData.slice(3, 6);
    
    const recentAvg = recent.reduce((sum, mood) => sum + getMoodValue(mood), 0) / recent.length;
    const olderAvg = older.length > 0 ? older.reduce((sum, mood) => sum + getMoodValue(mood), 0) / older.length : 0;
    
    if (recentAvg > olderAvg) return 'improving';
    if (recentAvg < olderAvg) return 'declining';
    return 'stable';
  };

  const getHabitCompletionRate = () => {
    if (habitData.length === 0) return 0;
    
    // Check if habits have been completed today
    const isCompleted = (habit: any) => {
      // Check different possible field names for completion status
      if (habit.completed_at) return true;
      if (habit.completed) return true;
      if (habit.status === 'completed') return true;
      return false;
    };
    
    const completed = habitData.filter(isCompleted).length;
    return Math.round((completed / habitData.length) * 100);
  };

  const currentDate = new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const moodTrend = getMoodTrend();
  const habitCompletionRate = getHabitCompletionRate();

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      {/* Header */}
      <ThemedView style={styles.header}>
        <ThemedView style={styles.headerTop}>
          <Button
            title="Back"
            onPress={() => router.back()}
            variant="outline"
            size="small"
            icon={<IconSymbol size={16} name="chevron.left" color={tintColor} />}
            style={styles.backButton}
            textStyle={styles.backButtonText}
          />
          <ThemedView style={styles.headerCenter}>
            <ThemedText type="title" style={styles.titleText}>Dashboard</ThemedText>
            <ThemedText type="caption" style={styles.dateText}>{currentDate}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.headerSpacer} />
        </ThemedView>
      </ThemedView>

      {/* Quick Stats */}
      <ThemedView style={styles.statsContainer}>
        <Card style={styles.statCard}>
          <IconSymbol size={32} name="face.smiling" color={accentColor} />
          <ThemedText type="heading" style={styles.statNumber}>
            {moodData.length}
          </ThemedText>
          <ThemedText type="caption">Mood Logs</ThemedText>
          <ThemedText type="caption" style={{ 
            color: moodTrend === 'improving' ? successColor : 
                   moodTrend === 'declining' ? accentColor : tintColor 
          }}>
            {moodTrend === 'improving' ? '↗ Improving' : 
             moodTrend === 'declining' ? '↘ Needs attention' : '→ Stable'}
          </ThemedText>
        </Card>

        <Card style={styles.statCard}>
          <IconSymbol size={32} name="checkmark.circle.fill" color={tintColor} />
          <ThemedText type="heading" style={styles.statNumber}>
            {habitCompletionRate}%
          </ThemedText>
          <ThemedText type="caption">Habits Today</ThemedText>
          <ThemedText type="caption" style={{ 
            color: habitCompletionRate >= 80 ? successColor : 
                   habitCompletionRate >= 50 ? accentColor : tintColor 
          }}>
            {habitData.filter(h => h.completed_at || h.completed || h.status === 'completed').length}/{habitData.length} completed
          </ThemedText>
        </Card>
      </ThemedView>

      {/* Recent Mood */}
      <Card style={styles.sectionCard}>
        <ThemedView style={styles.sectionHeader}>
          <ThemedText type="subtitle">Recent Mood</ThemedText>
          <TouchableOpacity onPress={() => router.push('/mood-history')}>
            <ThemedText type="link">View All</ThemedText>
          </TouchableOpacity>
        </ThemedView>
        
        {moodData.length > 0 ? (
          <ThemedView style={styles.moodList}>
            {moodData.slice(0, 3).map((mood, index) => {
              // Extract mood information from different possible structures
              const getMoodDisplay = () => {
                console.log('Extracting mood display from:', JSON.stringify(mood));
                
                // Check for pleasantness field
                if (mood.pleasantness) {
                  console.log('Found pleasantness:', mood.pleasantness);
                  return mood.pleasantness;
                }
                
                // Check for mood field
                if (mood.mood) {
                  console.log('Found mood:', mood.mood);
                  return mood.mood;
                }
                
                // Check for emotion field
                if (mood.emotion) {
                  console.log('Found emotion:', mood.emotion);
                  return mood.emotion;
                }
                
                // Check for feeling field
                if (mood.feeling) {
                  console.log('Found feeling:', mood.feeling);
                  return mood.feeling;
                }
                
                // If we have answers array (from mood assessment)
                if (mood.answers && Array.isArray(mood.answers) && mood.answers.length > 0) {
                  console.log('Found answers array:', mood.answers);
                  return mood.answers[0];
                }
                
                // Check for any field that might contain mood information
                const potentialMoodFields = ['state', 'status', 'description', 'note', 'text'];
                for (const field of potentialMoodFields) {
                  if (mood[field] && typeof mood[field] === 'string') {
                    console.log(`Found potential mood in ${field}:`, mood[field]);
                    return mood[field];
                  }
                }
                
                // Last resort: check if the object has a toString method or can be converted to string
                try {
                  if (typeof mood === 'object' && mood !== null) {
                    // If it's a simple object with one main property, use that
                    const keys = Object.keys(mood).filter(k => 
                      typeof mood[k] === 'string' && 
                      !k.includes('id') && 
                      !k.includes('date') && 
                      !k.includes('time')
                    );
                    
                    if (keys.length === 1) {
                      console.log('Using single string property:', keys[0], mood[keys[0]]);
                      return mood[keys[0]];
                    }
                  }
                } catch (e) {
                  console.log('Error extracting mood from object:', e);
                }
                
                // Last resort
                console.log('No mood information found, using default');
                return 'Mood entry';
              };
              
              // Get intensity from different possible fields
              const getIntensity = () => {
                if (mood.intensity) return mood.intensity;
                if (mood.strength) return mood.strength;
                if (mood.level) return mood.level;
                if (mood.answers && Array.isArray(mood.answers) && mood.answers.length > 1) {
                  return mood.answers[1];
                }
                return null;
              };
              
              // Determine if mood is positive
              const isPositive = () => {
                try {
                  // First check if we have direct access to the mood value
                  if (mood.pleasantness === 'Pleasant') return true;
                  if (mood.pleasantness === 'Unpleasant') return false;
                  
                  // Check for direct mood values
                  if (typeof mood.mood === 'string') {
                    const directMood = mood.mood.toLowerCase();
                    if (directMood === 'good' || directMood === 'happy' || directMood === 'pleasant') return true;
                    if (directMood === 'bad' || directMood === 'sad' || directMood === 'unpleasant') return false;
                  }
                  
                  // Get the mood display text for further analysis
                  const moodDisplay = getMoodDisplay();
                  if (!moodDisplay) return false; // Default to negative if no mood data
                  
                  const moodValue = moodDisplay.toLowerCase();
                  console.log('Analyzing mood value:', moodValue);
                  
                  // Check for negative indicators first (more conservative approach)
                  if (moodValue.includes('unpleasant') || 
                      moodValue.includes('bad') || 
                      moodValue.includes('sad') || 
                      moodValue.includes('angry') ||
                      moodValue.includes('anxious') ||
                      moodValue.includes('stress') ||
                      moodValue.includes('depress') ||
                      moodValue.includes('fear') ||
                      moodValue.includes('worry') ||
                      moodValue.includes('upset') ||
                      moodValue.includes('frustrat')) {
                    console.log('Detected negative mood');
                    return false;
                  }
                  
                  // Check for positive indicators
                  if (moodValue.includes('pleasant') || 
                      moodValue.includes('good') || 
                      moodValue.includes('happy') || 
                      moodValue.includes('joy') ||
                      moodValue.includes('calm') ||
                      moodValue.includes('content') ||
                      moodValue.includes('relax') ||
                      moodValue.includes('peace') ||
                      moodValue.includes('excite')) {
                    console.log('Detected positive mood');
                    return true;
                  }
                  
                  // Check for numeric values (1-10 scale)
                  const numericMatch = moodValue.match(/(\d+)/);
                  if (numericMatch) {
                    const numericValue = parseInt(numericMatch[1], 10);
                    if (!isNaN(numericValue)) {
                      // Assume scale of 1-10, with 6+ being positive (more conservative)
                      return numericValue >= 6;
                    }
                  }
                  
                  // If we have answers array, check the first answer
                  if (mood.answers && Array.isArray(mood.answers) && mood.answers.length > 0) {
                    const firstAnswer = mood.answers[0].toLowerCase();
                    if (firstAnswer.includes('unpleasant')) return false;
                    if (firstAnswer.includes('pleasant')) return true;
                  }
                  
                  // Default to negative if we're unsure
                  console.log('No clear mood indicators, defaulting to negative');
                  return false;
                } catch (e) {
                  console.log('Error determining mood positivity:', e);
                  return false; // Default to negative on error
                }
              };
              
              // Get date display
              const getDateDisplay = () => {
                try {
                  if (mood.created_at) {
                    return new Date(mood.created_at).toLocaleDateString();
                  }
                  if (mood.date) {
                    return new Date(mood.date).toLocaleDateString();
                  }
                  if (mood.timestamp) {
                    return new Date(mood.timestamp).toLocaleDateString();
                  }
                  return 'Recent';
                } catch (e) {
                  return 'Recent';
                }
              };
              
              return (
                <ThemedView key={index} style={styles.moodItem}>
                  <IconSymbol 
                    size={20} 
                    name={isPositive() ? 'face.smiling' : 'face.dashed'} 
                    color={isPositive() ? successColor : accentColor} 
                  />
                  <ThemedView style={styles.moodDetails}>
                    <ThemedText type="body">
                      {getMoodDisplay()}
                      {getIntensity() ? ` • ${getIntensity()}` : ''}
                    </ThemedText>
                    <ThemedText type="caption">
                      {getDateDisplay()}
                    </ThemedText>
                  </ThemedView>
                </ThemedView>
              );
            })}
          </ThemedView>
        ) : (
          <ThemedView style={styles.emptyStateContainer}>
            <IconSymbol size={32} name="face.smiling" color={accentColor} style={styles.emptyIcon} />
            <ThemedText type="body" style={styles.emptyText}>
              No mood logs yet
            </ThemedText>
            <ThemedText type="caption" style={styles.emptySubtext}>
              Start tracking your mood to see insights here
            </ThemedText>
            <Button
              title="Log Your Mood"
              onPress={() => router.push('/(tabs)/mood')}
              variant="outline"
              size="small"
              style={styles.emptyButton}
            />
          </ThemedView>
        )}
      </Card>

      {/* Today's Habits */}
      <Card style={styles.sectionCard}>
        <ThemedView style={styles.sectionHeader}>
          <ThemedText type="subtitle">Today's Habits</ThemedText>
          <TouchableOpacity onPress={() => router.push('/(tabs)/habits')}>
            <ThemedText type="link">Manage</ThemedText>
          </TouchableOpacity>
        </ThemedView>
        
        {habitData.length > 0 ? (
          <ThemedView style={styles.habitList}>
            {habitData.slice(0, 4).map((habit, index) => (
              <ThemedView key={index} style={styles.habitItem}>
                <IconSymbol 
                  size={20} 
                  name={(habit.completed_at || habit.completed || habit.status === 'completed') ? 'checkmark.circle.fill' : 'circle'} 
                  color={(habit.completed_at || habit.completed || habit.status === 'completed') ? successColor : accentColor} 
                />
                <ThemedText type="body" style={styles.habitName}>
                  {habit.name || habit.title || "Habit"}
                </ThemedText>
              </ThemedView>
            ))}
          </ThemedView>
        ) : (
          <ThemedView style={styles.emptyStateContainer}>
            <IconSymbol size={32} name="checkmark.circle" color={accentColor} style={styles.emptyIcon} />
            <ThemedText type="body" style={styles.emptyText}>
              No habits yet
            </ThemedText>
            <ThemedText type="caption" style={styles.emptySubtext}>
              Create healthy habits to track your progress
            </ThemedText>
            <Button
              title="Add Habit"
              onPress={() => router.push('/(tabs)/habits')}
              variant="outline"
              size="small"
              style={styles.emptyButton}
            />
          </ThemedView>
        )}
      </Card>

      {/* Recent Journal */}
      <Card style={styles.sectionCard}>
        <ThemedView style={styles.sectionHeader}>
          <ThemedText type="subtitle">Recent Journal</ThemedText>
          <TouchableOpacity onPress={() => router.push('/journal-history')}>
            <ThemedText type="link">View All</ThemedText>
          </TouchableOpacity>
        </ThemedView>
        
        {journalData.length > 0 ? (
          <ThemedView style={styles.journalList}>
            {journalData.slice(0, 2).map((entry, index) => (
              <ThemedView key={index} style={styles.journalItem}>
                <ThemedText type="body" numberOfLines={2}>
                  {entry.gratitude_1 || entry.learned || entry.accomplishment || 'Journal entry'}
                </ThemedText>
                <ThemedText type="caption">
                  {new Date(entry.created_at).toLocaleDateString()}
                </ThemedText>
              </ThemedView>
            ))}
          </ThemedView>
        ) : (
          <ThemedView style={styles.emptyStateContainer}>
            <IconSymbol size={32} name="book" color={accentColor} style={styles.emptyIcon} />
            <ThemedText type="body" style={styles.emptyText}>
              No journal entries yet
            </ThemedText>
            <ThemedText type="caption" style={styles.emptySubtext}>
              Start reflecting on your day with journaling
            </ThemedText>
            <Button
              title="Write Entry"
              onPress={() => router.push('/(tabs)/journal')}
              variant="outline"
              size="small"
              style={styles.emptyButton}
            />
          </ThemedView>
        )}
      </Card>

      {/* Quick Actions */}
      <Card style={styles.sectionCard}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>Quick Actions</ThemedText>
        <ThemedView style={styles.actionButtons}>
          <Button
            title="Log Mood"
            onPress={() => router.push('/(tabs)/mood')}
            variant="primary"
            style={styles.actionButton}
            icon={<IconSymbol size={20} name="face.smiling" color="#FAF9F6" />}
          />
          <Button
            title="Write Journal"
            onPress={() => router.push('/(tabs)/journal')}
            variant="secondary"
            style={styles.actionButton}
            icon={<IconSymbol size={20} name="book.fill" color="#FAF9F6" />}
          />
        </ThemedView>
      </Card>

      {/* Bottom spacing */}
      <ThemedView style={styles.bottomSpacer} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60,
  },
  header: {
    padding: Spacing.lg,
    backgroundColor: 'transparent',
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'transparent',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  headerSpacer: {
    width: 100, // Same width as back button to center the title
    backgroundColor: 'transparent',
  },
  backButton: {
    width: 100,
    minWidth: 100,
  },
  backButtonText: {
    fontSize: 14,
    flexShrink: 0,
  },
  titleText: {
    textAlign: 'center',
    flexShrink: 0,
  },
  dateText: {
    opacity: 0.7,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: Spacing.lg,
    gap: Spacing.md,
    backgroundColor: 'transparent',
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
    padding: Spacing.lg,
  },
  statNumber: {
    marginVertical: Spacing.sm,
  },
  sectionCard: {
    margin: Spacing.lg,
    marginTop: Spacing.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
    backgroundColor: 'transparent',
  },
  sectionTitle: {
    marginBottom: Spacing.md,
  },
  moodList: {
    backgroundColor: 'transparent',
  },
  moodItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    backgroundColor: 'transparent',
  },
  moodDetails: {
    marginLeft: Spacing.md,
    backgroundColor: 'transparent',
  },
  habitList: {
    backgroundColor: 'transparent',
  },
  habitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.sm,
    backgroundColor: 'transparent',
  },
  habitName: {
    marginLeft: Spacing.md,
  },
  journalList: {
    backgroundColor: 'transparent',
  },
  journalItem: {
    paddingVertical: Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    backgroundColor: 'transparent',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
    backgroundColor: 'transparent',
  },
  actionButton: {
    flex: 1,
  },
  emptyStateContainer: {
    alignItems: 'center',
    paddingVertical: Spacing.lg,
    backgroundColor: 'transparent',
  },
  emptyIcon: {
    opacity: 0.5,
    marginBottom: Spacing.sm,
  },
  emptyText: {
    textAlign: 'center',
    marginBottom: Spacing.xs,
  },
  emptySubtext: {
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: Spacing.md,
  },
  emptyButton: {
    minWidth: 120,
  },
  bottomSpacer: {
    height: 100,
    backgroundColor: 'transparent',
  },
});