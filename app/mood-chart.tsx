import { useEffect, useState } from 'react';
import { Alert, ScrollView, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import { router } from 'expo-router';
import * as SQLite from 'expo-sqlite';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';

interface MoodLog {
  id: number;
  date: string;
  pleasantness: string;
  intensity: string;
  main_contributor: string;
  frequency: string;
  created_at: string;
}

interface ChartData {
  date: string;
  logs: Array<{
    value: number;
    pleasantScore: number;
    intensityScore: number;
    id: number;
  }>;
}

const { width: screenWidth } = Dimensions.get('window');

export default function MoodChartScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = useThemeColor({}, 'tint');
  const textColor = useThemeColor({}, 'text');
  const cardColor = useThemeColor({}, 'card');
  const secondaryColor = useThemeColor({}, 'secondary');
  
  const [moodLogs, setMoodLogs] = useState<MoodLog[]>([]);
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'all'>('week');

  useEffect(() => {
    loadMoodLogs();
  }, []);

  useEffect(() => {
    processChartData();
  }, [moodLogs, selectedPeriod]);

  const loadMoodLogs = async () => {
    try {
      const db = await SQLite.openDatabaseAsync('mood_tracker.db');
      
      const result = await db.getAllAsync(
        'SELECT * FROM mood_logs ORDER BY date ASC, created_at ASC'
      );
      
      setMoodLogs(result as MoodLog[]);
    } catch (error) {
      console.error('Error loading mood logs:', error);
      Alert.alert('Error', 'Failed to load mood data.');
    }
  };

  const processChartData = () => {
    let filteredLogs = [...moodLogs];
    const now = new Date();
    
    if (selectedPeriod === 'week') {
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      filteredLogs = moodLogs.filter(log => new Date(log.date) >= weekAgo);
    } else if (selectedPeriod === 'month') {
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      filteredLogs = moodLogs.filter(log => new Date(log.date) >= monthAgo);
    }

    // Group logs by date and create stacked data
    const groupedByDate = filteredLogs.reduce((groups, log) => {
      const date = log.date;
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push({
        pleasantScore: log.pleasantness === 'Pleasant' ? 1 : -1,
        intensityScore: log.intensity === 'Intense' ? 2 : 1,
        id: log.id
      });
      return groups;
    }, {} as Record<string, Array<{pleasantScore: number, intensityScore: number, id: number}>>);

    // Convert to chart data with stacked entries
    const data = Object.entries(groupedByDate).map(([date, logs]) => ({
      date,
      logs: logs.map(log => ({
        value: log.pleasantScore * log.intensityScore,
        pleasantScore: log.pleasantScore,
        intensityScore: log.intensityScore,
        id: log.id
      }))
    }));

    setChartData(data);
  };

  const getFrequencyStats = () => {
    const frequencyCount = moodLogs.reduce((acc, log) => {
      acc[log.frequency] = (acc[log.frequency] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return frequencyCount;
  };

  const getPleasantnessStats = () => {
    const total = moodLogs.length;
    const pleasant = moodLogs.filter(log => log.pleasantness === 'Pleasant').length;
    const unpleasant = total - pleasant;
    
    return {
      pleasant: total > 0 ? Math.round((pleasant / total) * 100) : 0,
      unpleasant: total > 0 ? Math.round((unpleasant / total) * 100) : 0,
      total
    };
  };

  const renderSimpleChart = () => {
    if (chartData.length === 0) return null;

    const allValues = chartData.flatMap(d => d.logs.map(log => Math.abs(log.value)));
    const maxValue = Math.max(...allValues);
    const chartWidth = screenWidth - 80;
    const chartHeight = 200;
    const barWidth = Math.max(chartWidth / chartData.length - 4, 20);
    const stackSpacing = 2; // Space between stacked bars

    return (
      <ThemedView style={[styles.chartContainer, { backgroundColor: cardColor }]}>
        <ThemedText type="subtitle" style={styles.chartTitle}>Mood Trend</ThemedText>
        
        <ThemedView style={styles.periodSelector}>
          {(['week', 'month', 'all'] as const).map(period => (
            <TouchableOpacity
              key={period}
              style={[
                styles.periodButton,
                selectedPeriod === period && { backgroundColor: tintColor }
              ]}
              onPress={() => setSelectedPeriod(period)}
            >
              <ThemedText 
                style={[
                  styles.periodButtonText,
                  selectedPeriod === period && { color: 'white' }
                ]}
              >
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </ThemedView>

        <ThemedView style={styles.chart}>
          <ThemedView style={styles.yAxis}>
            <ThemedText style={styles.axisLabel}>Pleasant</ThemedText>
            <ThemedView style={[styles.axisLine, { backgroundColor: textColor + '20' }]} />
            <ThemedText style={styles.axisLabel}>Unpleasant</ThemedText>
          </ThemedView>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.chartScroll}>
            <ThemedView style={[styles.chartContent, { height: chartHeight }]}>
              {chartData.map((dayData, dayIndex) => {
                return (
                  <ThemedView key={dayIndex} style={styles.barContainer}>
                    <ThemedView style={styles.stackedBars}>
                      {dayData.logs.map((log, logIndex) => {
                        const barHeight = Math.abs(log.value) / maxValue * (chartHeight / 2 - 20);
                        const isPositive = log.value > 0;
                        const stackWidth = Math.max(barWidth / dayData.logs.length - stackSpacing, 8);
                        
                        return (
                          <ThemedView 
                            key={log.id}
                            style={[
                              styles.stackedBar,
                              {
                                width: stackWidth,
                                height: barHeight,
                                backgroundColor: isPositive ? '#4CAF50' : '#F44336',
                                marginTop: isPositive ? chartHeight / 2 - barHeight : chartHeight / 2,
                                marginLeft: logIndex > 0 ? stackSpacing : 0,
                                opacity: 0.8 + (logIndex * 0.2 / dayData.logs.length), // Slight opacity variation
                              }
                            ]}
                          />
                        );
                      })}
                    </ThemedView>
                    <ThemedText style={styles.dateLabel}>
                      {new Date(dayData.date).getDate()}
                      {dayData.logs.length > 1 && (
                        <ThemedText style={styles.countLabel}> ({dayData.logs.length})</ThemedText>
                      )}
                    </ThemedText>
                  </ThemedView>
                );
              })}
            </ThemedView>
          </ScrollView>
        </ThemedView>
      </ThemedView>
    );
  };

  const renderStats = () => {
    const pleasantnessStats = getPleasantnessStats();
    const frequencyStats = getFrequencyStats();

    return (
      <ThemedView style={styles.statsContainer}>
        <ThemedView style={[styles.statCard, { backgroundColor: cardColor }]}>
          <ThemedText type="subtitle" style={styles.statTitle}>Overall Mood</ThemedText>
          <ThemedView style={styles.statRow}>
            <ThemedView style={styles.statItem}>
              <ThemedText style={[styles.statValue, { color: '#4CAF50' }]}>
                {pleasantnessStats.pleasant}%
              </ThemedText>
              <ThemedText style={styles.statLabel}>Pleasant</ThemedText>
            </ThemedView>
            <ThemedView style={styles.statItem}>
              <ThemedText style={[styles.statValue, { color: '#F44336' }]}>
                {pleasantnessStats.unpleasant}%
              </ThemedText>
              <ThemedText style={styles.statLabel}>Unpleasant</ThemedText>
            </ThemedView>
          </ThemedView>
          <ThemedText style={styles.totalEntries}>
            Total entries: {pleasantnessStats.total}
          </ThemedText>
        </ThemedView>

        <ThemedView style={[styles.statCard, { backgroundColor: cardColor }]}>
          <ThemedText type="subtitle" style={styles.statTitle}>Frequency Patterns</ThemedText>
          {Object.entries(frequencyStats).map(([frequency, count]) => (
            <ThemedView key={frequency} style={styles.frequencyRow}>
              <ThemedText style={styles.frequencyLabel}>{frequency}:</ThemedText>
              <ThemedText style={styles.frequencyCount}>{count} times</ThemedText>
            </ThemedView>
          ))}
        </ThemedView>
      </ThemedView>
    );
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <IconSymbol size={24} name="chevron.left" color={tintColor} />
        </TouchableOpacity>
        <ThemedText type="title">Mood Chart</ThemedText>
        <TouchableOpacity onPress={() => router.push('/mood-history')}>
          <IconSymbol size={24} name="calendar" color={tintColor} />
        </TouchableOpacity>
      </ThemedView>

      {moodLogs.length === 0 ? (
        <ThemedView style={[styles.emptyState, { backgroundColor: cardColor }]}>
          <IconSymbol size={48} name="chart.bar" color={tintColor} />
          <ThemedText type="subtitle" style={styles.emptyTitle}>
            No Data Available
          </ThemedText>
          <ThemedText style={styles.emptySubtext}>
            Start tracking your mood to see charts and insights.
          </ThemedText>
          <TouchableOpacity
            style={[styles.startButton, { backgroundColor: tintColor }]}
            onPress={() => router.push('/(tabs)/mood')}
          >
            <ThemedText style={styles.startButtonText}>Start Mood Assessment</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      ) : (
        <>
          {renderSimpleChart()}
          {renderStats()}
        </>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    backgroundColor: 'transparent',
  },
  emptyState: {
    margin: 20,
    padding: 40,
    borderRadius: 16,
    alignItems: 'center',
  },
  emptyTitle: {
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    opacity: 0.7,
    textAlign: 'center',
    marginBottom: 24,
  },
  startButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
  },
  startButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  chartContainer: {
    margin: 20,
    padding: 20,
    borderRadius: 16,
  },
  chartTitle: {
    marginBottom: 16,
    textAlign: 'center',
  },
  periodSelector: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
    backgroundColor: 'transparent',
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  chart: {
    flexDirection: 'row',
    backgroundColor: 'transparent',
  },
  yAxis: {
    width: 80,
    height: 200,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 20,
    backgroundColor: 'transparent',
  },
  axisLabel: {
    fontSize: 12,
    fontWeight: '600',
    opacity: 0.7,
  },
  axisLine: {
    width: '100%',
    height: 1,
  },
  chartScroll: {
    flex: 1,
  },
  chartContent: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 10,
    backgroundColor: 'transparent',
  },
  barContainer: {
    alignItems: 'center',
    marginHorizontal: 2,
    backgroundColor: 'transparent',
  },
  stackedBars: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: 'transparent',
  },
  stackedBar: {
    borderRadius: 2,
  },
  bar: {
    borderRadius: 2,
  },
  dateLabel: {
    fontSize: 10,
    marginTop: 4,
    opacity: 0.7,
  },
  countLabel: {
    fontSize: 8,
    opacity: 0.5,
  },
  statsContainer: {
    padding: 20,
    backgroundColor: 'transparent',
  },
  statCard: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
  },
  statTitle: {
    marginBottom: 16,
    textAlign: 'center',
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
    backgroundColor: 'transparent',
  },
  statItem: {
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    opacity: 0.7,
  },
  totalEntries: {
    textAlign: 'center',
    fontSize: 12,
    opacity: 0.7,
  },
  frequencyRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
    backgroundColor: 'transparent',
  },
  frequencyLabel: {
    fontSize: 14,
    fontWeight: '600',
  },
  frequencyCount: {
    fontSize: 14,
    opacity: 0.7,
  },
});