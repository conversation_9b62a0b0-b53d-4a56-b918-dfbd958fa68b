import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert, Dimensions, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { DatabaseManager } from '@/utils/database';

interface DistractionEntry {
  id: number;
  what: string;
  when_occurred: string;
  why: string;
  date: string;
  timestamp: string;
}

interface ChartData {
  date: string;
  count: number;
  entries: DistractionEntry[];
}

const { width: screenWidth } = Dimensions.get('window');

export default function DistractionStatsScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = useThemeColor({}, 'tint');
  const textColor = useThemeColor({}, 'text');
  const cardColor = useThemeColor({}, 'card');
  
  const [entries, setEntries] = useState<DistractionEntry[]>([]);
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'all'>('week');
  const [dbManager] = useState(() => DatabaseManager.getInstance());

  useEffect(() => {
    loadEntries();
  }, []);

  useEffect(() => {
    processChartData();
  }, [entries, selectedPeriod]);

  const loadEntries = async () => {
    try {
      const db = dbManager.getDatabase();
      const result = await db.getAllAsync(
        'SELECT * FROM distraction_entries ORDER BY date ASC'
      );
      setEntries(result as DistractionEntry[]);
    } catch (error) {
      console.error('Error loading distraction entries:', error);
      Alert.alert('Error', 'Failed to load distraction data');
    }
  };

  const processChartData = () => {
    let filteredEntries = [...entries];
    const now = new Date();
    
    if (selectedPeriod === 'week') {
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      filteredEntries = entries.filter(entry => new Date(entry.date) >= weekAgo);
    } else if (selectedPeriod === 'month') {
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      filteredEntries = entries.filter(entry => new Date(entry.date) >= monthAgo);
    }

    // Group by date
    const groupedByDate = filteredEntries.reduce((groups, entry) => {
      const date = entry.date;
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(entry);
      return groups;
    }, {} as Record<string, DistractionEntry[]>);

    // Convert to chart data
    const data = Object.entries(groupedByDate).map(([date, dayEntries]) => ({
      date,
      count: dayEntries.length,
      entries: dayEntries
    }));

    setChartData(data.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()));
  };

  const renderLineChart = () => {
    if (chartData.length === 0) return null;

    const maxCount = Math.max(...chartData.map(d => d.count));
    const chartWidth = screenWidth - 80;
    const chartHeight = 200;
    const pointRadius = 4;

    return (
      <ThemedView style={[styles.chartContainer, { backgroundColor: cardColor }]}>
        <ThemedText type="subtitle" style={styles.chartTitle}>Distractions Per Day</ThemedText>
        
        <ThemedView style={styles.periodSelector}>
          {(['week', 'month', 'all'] as const).map(period => (
            <TouchableOpacity
              key={period}
              style={[
                styles.periodButton,
                selectedPeriod === period && { backgroundColor: tintColor }
              ]}
              onPress={() => setSelectedPeriod(period)}
            >
              <ThemedText 
                style={[
                  styles.periodButtonText,
                  selectedPeriod === period && { color: 'white' }
                ]}
              >
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </ThemedText>
            </TouchableOpacity>
          ))}
        </ThemedView>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.chartScroll}>
          <ThemedView style={[styles.chartContent, { width: Math.max(chartWidth, chartData.length * 60), height: chartHeight }]}>
            {/* Y-axis labels */}
            <ThemedView style={styles.yAxisLabels}>
              {Array.from({ length: maxCount + 1 }, (_, i) => maxCount - i).map(value => (
                <ThemedText key={value} style={styles.yAxisLabel}>{value}</ThemedText>
              ))}
            </ThemedView>

            {/* Chart area */}
            <ThemedView style={styles.chartArea}>
              {/* Grid lines */}
              {Array.from({ length: maxCount + 1 }, (_, i) => (
                <ThemedView 
                  key={i} 
                  style={[
                    styles.gridLine, 
                    { 
                      top: (i / maxCount) * (chartHeight - 40),
                      backgroundColor: textColor + '20'
                    }
                  ]} 
                />
              ))}

              {/* Data points and lines */}
              {chartData.map((point, index) => {
                const x = (index / (chartData.length - 1)) * (chartWidth - 40);
                const y = chartHeight - 40 - (point.count / maxCount) * (chartHeight - 40);
                
                return (
                  <ThemedView key={point.date}>
                    {/* Line to next point */}
                    {index < chartData.length - 1 && (
                      <ThemedView
                        style={[
                          styles.chartLine,
                          {
                            left: x + pointRadius,
                            top: y + pointRadius,
                            width: Math.sqrt(
                              Math.pow((chartWidth - 40) / (chartData.length - 1), 2) +
                              Math.pow(
                                (chartData[index + 1].count / maxCount) * (chartHeight - 40) -
                                (point.count / maxCount) * (chartHeight - 40), 2
                              )
                            ),
                            transform: [{
                              rotate: `${Math.atan2(
                                ((chartData[index + 1].count / maxCount) * (chartHeight - 40)) -
                                ((point.count / maxCount) * (chartHeight - 40)),
                                (chartWidth - 40) / (chartData.length - 1)
                              )}rad`
                            }],
                            backgroundColor: tintColor
                          }
                        ]}
                      />
                    )}
                    
                    {/* Data point */}
                    <ThemedView
                      style={[
                        styles.dataPoint,
                        {
                          left: x,
                          top: y,
                          backgroundColor: tintColor
                        }
                      ]}
                    />
                  </ThemedView>
                );
              })}

              {/* X-axis labels */}
              <ThemedView style={styles.xAxisLabels}>
                {chartData.map((point, index) => (
                  <ThemedText 
                    key={point.date} 
                    style={[
                      styles.xAxisLabel,
                      { left: (index / (chartData.length - 1)) * (chartWidth - 40) - 15 }
                    ]}
                  >
                    {new Date(point.date).getDate()}
                  </ThemedText>
                ))}
              </ThemedView>
            </ThemedView>
          </ThemedView>
        </ScrollView>
      </ThemedView>
    );
  };

  const renderStats = () => {
    const totalDistractions = entries.length;
    const avgPerDay = chartData.length > 0 ? (totalDistractions / chartData.length).toFixed(1) : '0';
    const maxDay = chartData.reduce((max, day) => day.count > max.count ? day : max, { count: 0, date: '', entries: [] });
    
    // Most common distractions
    const distractionTypes = entries.reduce((types, entry) => {
      const what = entry.what.toLowerCase();
      types[what] = (types[what] || 0) + 1;
      return types;
    }, {} as Record<string, number>);
    
    const topDistractions = Object.entries(distractionTypes)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);

    return (
      <ThemedView style={styles.statsContainer}>
        <ThemedView style={[styles.statCard, { backgroundColor: cardColor }]}>
          <ThemedText type="subtitle" style={styles.statTitle}>Overview</ThemedText>
          <ThemedView style={styles.statRow}>
            <ThemedText style={styles.statLabel}>Total Distractions:</ThemedText>
            <ThemedText style={styles.statValue}>{totalDistractions}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.statRow}>
            <ThemedText style={styles.statLabel}>Average per Day:</ThemedText>
            <ThemedText style={styles.statValue}>{avgPerDay}</ThemedText>
          </ThemedView>
          {maxDay.count > 0 && (
            <ThemedView style={styles.statRow}>
              <ThemedText style={styles.statLabel}>Highest Day:</ThemedText>
              <ThemedText style={styles.statValue}>
                {maxDay.count} on {new Date(maxDay.date).toLocaleDateString()}
              </ThemedText>
            </ThemedView>
          )}
        </ThemedView>

        {topDistractions.length > 0 && (
          <ThemedView style={[styles.statCard, { backgroundColor: cardColor }]}>
            <ThemedText type="subtitle" style={styles.statTitle}>Most Common Distractions</ThemedText>
            {topDistractions.map(([distraction, count], index) => (
              <ThemedView key={distraction} style={styles.distractionRow}>
                <ThemedText style={styles.distractionRank}>{index + 1}.</ThemedText>
                <ThemedText style={styles.distractionName}>{distraction}</ThemedText>
                <ThemedText style={styles.distractionCount}>{count}x</ThemedText>
              </ThemedView>
            ))}
          </ThemedView>
        )}
      </ThemedView>
    );
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <IconSymbol size={24} name="chevron.left" color={tintColor} />
        </TouchableOpacity>
        <ThemedText type="title">Distraction Statistics</ThemedText>
        <ThemedView style={{ width: 24 }} />
      </ThemedView>

      {entries.length === 0 ? (
        <ThemedView style={[styles.emptyState, { backgroundColor: cardColor }]}>
          <IconSymbol size={48} name="chart.line.uptrend.xyaxis" color={tintColor} />
          <ThemedText type="subtitle" style={styles.emptyTitle}>
            No Data Available
          </ThemedText>
          <ThemedText style={styles.emptySubtext}>
            Start logging distractions to see statistics and trends.
          </ThemedText>
        </ThemedView>
      ) : (
        <>
          {renderLineChart()}
          {renderStats()}
        </>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  chartContainer: {
    margin: 20,
    padding: 16,
    borderRadius: 12,
  },
  chartTitle: {
    textAlign: 'center',
    marginBottom: 16,
  },
  periodSelector: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    marginHorizontal: 4,
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  chartScroll: {
    marginHorizontal: -16,
  },
  chartContent: {
    paddingHorizontal: 16,
    position: 'relative',
  },
  yAxisLabels: {
    position: 'absolute',
    left: 0,
    top: 0,
    height: 160,
    justifyContent: 'space-between',
    width: 30,
  },
  yAxisLabel: {
    fontSize: 12,
    textAlign: 'right',
  },
  chartArea: {
    marginLeft: 40,
    position: 'relative',
  },
  gridLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
  },
  chartLine: {
    position: 'absolute',
    height: 2,
    transformOrigin: 'left center',
  },
  dataPoint: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  xAxisLabels: {
    position: 'absolute',
    bottom: -20,
    left: 0,
    right: 0,
  },
  xAxisLabel: {
    position: 'absolute',
    fontSize: 12,
    width: 30,
    textAlign: 'center',
  },
  statsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  statCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  statTitle: {
    marginBottom: 12,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statLabel: {
    fontSize: 14,
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  distractionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  distractionRank: {
    fontSize: 14,
    fontWeight: '600',
    width: 20,
  },
  distractionName: {
    flex: 1,
    fontSize: 14,
    marginLeft: 8,
    textTransform: 'capitalize',
  },
  distractionCount: {
    fontSize: 14,
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
    margin: 20,
    borderRadius: 12,
  },
  emptyTitle: {
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
  },
});