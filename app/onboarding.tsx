import React, { useState } from 'react';
import { ScrollView, StyleSheet, Dimensions } from 'react-native';
import { router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Spacing } from '@/constants/Spacing';

const { width } = Dimensions.get('window');

// Updated with Tranquil Growth color palette
const onboardingSteps = [
  {
    title: "Welcome to Your Safe Space",
    description: "A private space for tracking your mental wellness journey. Your data stays secure and private on your device.",
    icon: "heart.fill",
    color: "#7A936B" // Muted Sage Green (primary)
  },
  {
    title: "Track Your Mood",
    description: "Log your daily emotions and feelings. Understand patterns and triggers to improve your mental health.",
    icon: "face.smiling",
    color: "#E07A5F" // Soft Coral/Terracotta (accent)
  },
  {
    title: "Build Healthy Habits",
    description: "Create and track positive daily habits that support your wellbeing and personal growth.",
    icon: "checkmark.circle.fill",
    color: "#7A936B" // Muted Sage Green (primary)
  },
  {
    title: "Reflect & Journal",
    description: "Practice gratitude and reflection through guided journaling prompts and free writing.",
    icon: "book.fill",
    color: "#E07A5F" // Soft Coral/Terracotta (accent)
  },
  {
    title: "Breathe & Relax",
    description: "Access breathing exercises and mindfulness tools to help manage stress and anxiety.",
    icon: "lungs.fill",
    color: "#7A936B" // Muted Sage Green (primary)
  }
];

export default function OnboardingScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = useThemeColor({}, 'tint');
  const accentColor = useThemeColor({}, 'accent');
  const [currentStep, setCurrentStep] = useState(0);

  const handleNext = () => {
    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete onboarding and navigate to main app
      router.replace('/(tabs)');
    }
  };

  const handleSkip = () => {
    router.replace('/(tabs)');
  };

  const currentStepData = onboardingSteps[currentStep];

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      <ThemedView style={styles.header}>
        <ThemedText type="caption" style={styles.stepIndicator}>
          {currentStep + 1} of {onboardingSteps.length}
        </ThemedText>
        
        {currentStep < onboardingSteps.length - 1 && (
          <Button
            title="Skip"
            onPress={handleSkip}
            variant="outline"
            size="small"
          />
        )}
      </ThemedView>

      <ThemedView style={styles.content}>
        <Card style={styles.stepCard}>
          <ThemedView style={styles.iconContainer}>
            <IconSymbol 
              size={80} 
              name={currentStepData.icon as any} 
              color={currentStepData.color} 
            />
          </ThemedView>
          
          <ThemedText type="title" style={styles.title}>
            {currentStepData.title}
          </ThemedText>
          
          <ThemedText type="body" style={styles.description}>
            {currentStepData.description}
          </ThemedText>
        </Card>

        {/* Progress Indicator */}
        <ThemedView style={styles.progressContainer}>
          {onboardingSteps.map((_, index) => (
            <ThemedView
              key={index}
              style={[
                styles.progressDot,
                {
                  backgroundColor: index <= currentStep ? tintColor : 'rgba(0,0,0,0.2)',
                }
              ]}
            />
          ))}
        </ThemedView>

        {/* Navigation Buttons */}
        <ThemedView style={styles.buttonContainer}>
          {currentStep > 0 && (
            <Button
              title="Previous"
              onPress={() => setCurrentStep(currentStep - 1)}
              variant="outline"
              style={styles.navButton}
            />
          )}
          
          <Button
            title={currentStep === onboardingSteps.length - 1 ? "Get Started" : "Next"}
            onPress={handleNext}
            variant="primary"
            style={[styles.navButton, { flex: currentStep === 0 ? 1 : 0.6 }]}
            icon={
              currentStep === onboardingSteps.length - 1 ? 
                <IconSymbol size={20} name="arrow.right" color="#FAF9F6" /> : 
                undefined
            }
          />
        </ThemedView>

        {/* Features Preview */}
        {currentStep === onboardingSteps.length - 1 && (
          <Card style={styles.featuresCard}>
            <ThemedText type="subtitle" style={styles.featuresTitle}>
              What you'll get:
            </ThemedText>
            
            <ThemedView style={styles.featuresList}>
              <ThemedView style={styles.featureItem}>
                <IconSymbol size={16} name="checkmark.circle.fill" color={tintColor} />
                <ThemedText type="caption" style={styles.featureText}>
                  Private & secure data storage
                </ThemedText>
              </ThemedView>
              
              <ThemedView style={styles.featureItem}>
                <IconSymbol size={16} name="checkmark.circle.fill" color={tintColor} />
                <ThemedText type="caption" style={styles.featureText}>
                  Personalized insights & trends
                </ThemedText>
              </ThemedView>
              
              <ThemedView style={styles.featureItem}>
                <IconSymbol size={16} name="checkmark.circle.fill" color={tintColor} />
                <ThemedText type="caption" style={styles.featureText}>
                  Daily reminders & motivation
                </ThemedText>
              </ThemedView>
              
              <ThemedView style={styles.featureItem}>
                <IconSymbol size={16} name="checkmark.circle.fill" color={tintColor} />
                <ThemedText type="caption" style={styles.featureText}>
                  Guided wellness activities
                </ThemedText>
              </ThemedView>
            </ThemedView>
          </Card>
        )}
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    backgroundColor: 'transparent',
  },
  stepIndicator: {
    opacity: 0.7,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
    backgroundColor: 'transparent',
  },
  stepCard: {
    alignItems: 'center',
    padding: Spacing.xl,
    marginBottom: Spacing.xl,
  },
  iconContainer: {
    marginBottom: Spacing.lg,
    backgroundColor: 'transparent',
  },
  title: {
    textAlign: 'center',
    marginBottom: Spacing.md,
  },
  description: {
    textAlign: 'center',
    opacity: 0.8,
    lineHeight: 24,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.xl,
    gap: Spacing.sm,
    backgroundColor: 'transparent',
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: Spacing.md,
    marginBottom: Spacing.lg,
    backgroundColor: 'transparent',
  },
  navButton: {
    flex: 0.4,
  },
  featuresCard: {
    marginTop: Spacing.md,
  },
  featuresTitle: {
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  featuresList: {
    backgroundColor: 'transparent',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
    backgroundColor: 'transparent',
  },
  featureText: {
    marginLeft: Spacing.sm,
    flex: 1,
  },
});