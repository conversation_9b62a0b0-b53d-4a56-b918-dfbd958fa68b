import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { DatabaseManager } from '@/utils/database';

interface EmotionEntry {
  id: number;
  primary_emotion: string;
  secondary_emotion?: string;
  reflection: string;
  date: string;
  timestamp: string;
}

interface CalendarDay {
  date: string;
  count: number;
  entries: EmotionEntry[];
  isCurrentMonth: boolean;
}

export default function EmotionHistoryScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = useThemeColor({}, 'tint');
  const textColor = useThemeColor({}, 'text');
  const cardColor = useThemeColor({}, 'card');
  const borderColor = useThemeColor({}, 'border');
  
  const [entries, setEntries] = useState<EmotionEntry[]>([]);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [calendarData, setCalendarData] = useState<CalendarDay[]>([]);
  const [selectedDay, setSelectedDay] = useState<CalendarDay | null>(null);
  const [dbManager] = useState(() => DatabaseManager.getInstance());

  useEffect(() => {
    loadEntries();
  }, [currentDate]);

  const loadEntries = async () => {
    try {
      const db = dbManager.getDatabase();
      const result = await db.getAllAsync(
        'SELECT * FROM emotion_reflections ORDER BY date DESC, timestamp DESC'
      );
      setEntries(result as EmotionEntry[]);
      generateCalendarData(result as EmotionEntry[]);
    } catch (error) {
      console.error('Error loading emotion entries:', error);
      Alert.alert('Error', 'Failed to load emotion entries');
    }
  };

  const generateCalendarData = (emotionEntries: EmotionEntry[]) => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    // Get first day of month and how many days in month
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    
    // Get first day of week (0 = Sunday)
    const startingDayOfWeek = firstDay.getDay();
    
    const calendarDays: CalendarDay[] = [];
    
    // Add days from previous month to fill the first week
    const prevMonth = new Date(year, month - 1, 0);
    for (let i = startingDayOfWeek - 1; i >= 0; i--) {
      const date = new Date(year, month - 1, prevMonth.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      const dayEntries = emotionEntries.filter(entry => entry.date === dateStr);
      
      calendarDays.push({
        date: dateStr,
        count: dayEntries.length,
        entries: dayEntries,
        isCurrentMonth: false
      });
    }
    
    // Add days of current month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const dateStr = date.toISOString().split('T')[0];
      const dayEntries = emotionEntries.filter(entry => entry.date === dateStr);
      
      calendarDays.push({
        date: dateStr,
        count: dayEntries.length,
        entries: dayEntries,
        isCurrentMonth: true
      });
    }
    
    // Add days from next month to fill the last week
    const totalCells = Math.ceil(calendarDays.length / 7) * 7;
    let nextMonthDay = 1;
    for (let i = calendarDays.length; i < totalCells; i++) {
      const date = new Date(year, month + 1, nextMonthDay);
      const dateStr = date.toISOString().split('T')[0];
      const dayEntries = emotionEntries.filter(entry => entry.date === dateStr);
      
      calendarDays.push({
        date: dateStr,
        count: dayEntries.length,
        entries: dayEntries,
        isCurrentMonth: false
      });
      nextMonthDay++;
    }
    
    setCalendarData(calendarDays);
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
    setSelectedDay(null);
  };

  const getIntensityColor = (count: number) => {
    if (count === 0) return 'transparent';
    if (count === 1) return tintColor + '40';
    if (count <= 3) return tintColor + '70';
    return tintColor;
  };

  const getEmotionEmoji = (emotion: string) => {
    const emotionLower = emotion.toLowerCase();
    if (emotionLower.includes('joy') || emotionLower.includes('happy')) return '😊';
    if (emotionLower.includes('sad')) return '😢';
    if (emotionLower.includes('anger') || emotionLower.includes('angry')) return '😠';
    if (emotionLower.includes('fear') || emotionLower.includes('anxious')) return '😰';
    if (emotionLower.includes('surprise')) return '😲';
    if (emotionLower.includes('disgust')) return '🤢';
    if (emotionLower.includes('trust')) return '🤗';
    if (emotionLower.includes('anticipation')) return '🤔';
    return '💭';
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <IconSymbol size={24} name="chevron.left" color={tintColor} />
        </TouchableOpacity>
        <ThemedText type="title">Emotion History</ThemedText>
        <ThemedView style={{ width: 24 }} />
      </ThemedView>

      {/* Calendar Header */}
      <ThemedView style={[styles.calendarHeader, { backgroundColor: cardColor }]}>
        <TouchableOpacity onPress={() => navigateMonth('prev')}>
          <IconSymbol size={20} name="chevron.left" color={tintColor} />
        </TouchableOpacity>
        <ThemedText type="subtitle">
          {currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
        </ThemedText>
        <TouchableOpacity onPress={() => navigateMonth('next')}>
          <IconSymbol size={20} name="chevron.right" color={tintColor} />
        </TouchableOpacity>
      </ThemedView>

      {/* Calendar */}
      <ThemedView style={[styles.calendar, { backgroundColor: cardColor }]}>
        <ThemedView style={styles.weekDays}>
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
            <ThemedText key={day} style={styles.weekDay}>{day}</ThemedText>
          ))}
        </ThemedView>
        
        <ThemedView style={styles.calendarGrid}>
          {calendarData.map((day, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.calendarDay,
                { backgroundColor: getIntensityColor(day.count) },
                !day.isCurrentMonth && styles.otherMonth,
                selectedDay?.date === day.date && { borderColor: tintColor, borderWidth: 2 }
              ]}
              onPress={() => setSelectedDay(day.count > 0 ? day : null)}
            >
              <ThemedText style={[
                styles.dayNumber,
                !day.isCurrentMonth && styles.otherMonthText,
                day.count > 2 && { color: 'white' }
              ]}>
                {new Date(day.date).getDate()}
              </ThemedText>
              {day.count > 0 && (
                <ThemedText style={[styles.countBadge, day.count > 2 && { color: 'white' }]}>
                  {day.count}
                </ThemedText>
              )}
            </TouchableOpacity>
          ))}
        </ThemedView>
      </ThemedView>

      {/* Selected Day Details */}
      {selectedDay && selectedDay.entries.length > 0 && (
        <ThemedView style={[styles.dayDetails, { backgroundColor: cardColor }]}>
          <ThemedText type="subtitle" style={styles.dayDetailsTitle}>
            {new Date(selectedDay.date).toLocaleDateString('en-US', { 
              weekday: 'long', 
              month: 'long', 
              day: 'numeric' 
            })}
          </ThemedText>
          
          {selectedDay.entries.map((entry, index) => (
            <ThemedView key={entry.id} style={[styles.entryCard, { borderColor }]}>
              <ThemedView style={styles.entryHeader}>
                <ThemedText style={styles.entryEmotion}>
                  {getEmotionEmoji(entry.primary_emotion)} {entry.primary_emotion}
                </ThemedText>
                <ThemedText style={styles.entryTime}>
                  {new Date(entry.timestamp).toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit'
                  })}
                </ThemedText>
              </ThemedView>
              {entry.reflection && (
                <ThemedText style={styles.entryReflection}>{entry.reflection}</ThemedText>
              )}
            </ThemedView>
          ))}
        </ThemedView>
      )}

      {entries.length === 0 && (
        <ThemedView style={[styles.emptyState, { backgroundColor: cardColor }]}>
          <IconSymbol size={48} name="circle.grid.hex" color={tintColor} />
          <ThemedText style={styles.emptyTitle}>No emotions logged</ThemedText>
          <ThemedText style={styles.emptySubtitle}>
            Start tracking your emotions to see patterns and insights
          </ThemedText>
        </ThemedView>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  calendarHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    marginHorizontal: 20,
    borderRadius: 12,
    marginBottom: 16,
  },
  calendar: {
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  weekDays: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  weekDay: {
    flex: 1,
    textAlign: 'center',
    fontSize: 12,
    fontWeight: '600',
    opacity: 0.6,
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  calendarDay: {
    width: '14.28%',
    aspectRatio: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    margin: 1,
    position: 'relative',
  },
  otherMonth: {
    opacity: 0.3,
  },
  dayNumber: {
    fontSize: 14,
    fontWeight: '500',
  },
  otherMonthText: {
    opacity: 0.5,
  },
  countBadge: {
    fontSize: 10,
    fontWeight: 'bold',
    position: 'absolute',
    bottom: 2,
  },
  dayDetails: {
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  dayDetailsTitle: {
    marginBottom: 16,
  },
  entryCard: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  entryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  entryEmotion: {
    fontSize: 16,
    fontWeight: '600',
  },
  entryTime: {
    fontSize: 12,
    opacity: 0.6,
  },
  entryReflection: {
    fontSize: 14,
    opacity: 0.8,
    lineHeight: 20,
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
    marginHorizontal: 20,
    borderRadius: 12,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    opacity: 0.6,
    textAlign: 'center',
    lineHeight: 20,
  },
});
