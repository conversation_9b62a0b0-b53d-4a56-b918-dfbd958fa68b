import { useState } from 'react';
import { Al<PERSON>, ScrollView, StyleSheet, TextInput, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import * as SQLite from 'expo-sqlite';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';

export default function MoodAssessmentScreen() {
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = useThemeColor({}, 'tint');
  const textColor = useThemeColor({}, 'text');
  const cardColor = useThemeColor({}, 'card');
  const secondaryColor = useThemeColor({}, 'secondary');
  
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedMood, setSelectedMood] = useState<number | null>(null);
  const [pleasantness, setPleasantness] = useState<'pleasant' | 'unpleasant' | null>(null);
  const [intensity, setIntensity] = useState<'mild' | 'intense' | null>(null);
  const [mainContributor, setMainContributor] = useState('');
  const [thoughtsReactions, setThoughtsReactions] = useState('');
  const [frequency, setFrequency] = useState<'rarely' | 'sometimes' | 'often' | 'very_often' | null>(null);
  const [possibleFeelings, setPossibleFeelings] = useState<string[]>([]);
  const [reasonsWhy, setReasonsWhy] = useState('');

  const moodEmojis = ['😢', '😕', '😐', '🙂', '😊', '😄', '😁', '🤩', '😍', '🥰'];
  
  const feelingOptions = [
    'Anxious', 'Stressed', 'Overwhelmed', 'Sad', 'Angry', 'Frustrated',
    'Happy', 'Excited', 'Grateful', 'Peaceful', 'Confident', 'Hopeful',
    'Lonely', 'Confused', 'Tired', 'Energetic', 'Calm', 'Worried'
  ];

  const frequencyOptions = [
    { key: 'rarely', label: 'Rarely (1-2 times)', description: 'This feeling is uncommon for me' },
    { key: 'sometimes', label: 'Sometimes (3-7 times)', description: 'I feel this way occasionally' },
    { key: 'often', label: 'Often (8-15 times)', description: 'This is a frequent feeling for me' },
    { key: 'very_often', label: 'Very Often (16+ times)', description: 'I feel this way most of the time' }
  ];

  const toggleFeeling = (feeling: string) => {
    setPossibleFeelings(prev => 
      prev.includes(feeling) 
        ? prev.filter(f => f !== feeling)
        : [...prev, feeling]
    );
  };

  const nextStep = () => {
    if (currentStep < 7) {
      setCurrentStep(currentStep + 1);
    } else {
      saveMoodAssessment();
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1: return selectedMood !== null;
      case 2: return pleasantness !== null;
      case 3: return intensity !== null;
      case 4: return mainContributor.trim() !== '';
      case 5: return thoughtsReactions.trim() !== '';
      case 6: return frequency !== null;
      case 7: return possibleFeelings.length > 0 || reasonsWhy.trim() !== '';
      default: return false;
    }
  };

  const saveMoodAssessment = async () => {
    try {
      const database = await SQLite.openDatabaseAsync('mood_tracker.db');
      
      // Ensure the table exists with extended columns for detailed assessments
      await database.execAsync(`
        CREATE TABLE IF NOT EXISTS mood_entries (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          mood INTEGER NOT NULL,
          emotion TEXT,
          notes TEXT,
          date TEXT NOT NULL,
          timestamp TEXT NOT NULL,
          assessment_type TEXT DEFAULT 'quick',
          pleasantness TEXT,
          intensity TEXT,
          main_contributor TEXT,
          thoughts_reactions TEXT,
          frequency TEXT,
          possible_feelings TEXT,
          reasons_why TEXT
        );
      `);

      const now = new Date();
      const date = now.toISOString().split('T')[0];
      const timestamp = now.toISOString();

      // Create a comprehensive notes field that includes all assessment data
      const detailedNotes = `Detailed Assessment:
• Pleasantness: ${pleasantness}
• Intensity: ${intensity}
• Main Contributor: ${mainContributor}
• Thoughts/Reactions: ${thoughtsReactions}
• Frequency: ${frequency}
• Feelings: ${possibleFeelings.join(', ')}
• Reasons: ${reasonsWhy}`.trim();

      // Save to the same table as quick entries but with detailed data
      await database.runAsync(`
        INSERT INTO mood_entries (
          mood, emotion, notes, date, timestamp, assessment_type,
          pleasantness, intensity, main_contributor, thoughts_reactions,
          frequency, possible_feelings, reasons_why
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        selectedMood,
        possibleFeelings.length > 0 ? possibleFeelings[0] : null, // Primary emotion
        detailedNotes,
        date,
        timestamp,
        'detailed',
        pleasantness,
        intensity,
        mainContributor,
        thoughtsReactions,
        frequency,
        JSON.stringify(possibleFeelings),
        reasonsWhy
      ]);

      Alert.alert(
        'Assessment Complete',
        'Your detailed mood assessment has been saved and will appear in your mood history.',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    } catch (error) {
      console.error('Error saving mood assessment:', error);
      Alert.alert('Error', 'Failed to save mood assessment');
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <ThemedView style={styles.stepContainer}>
            <ThemedText style={styles.stepTitle}>How are you feeling right now?</ThemedText>
            <ThemedText style={styles.stepSubtitle}>Rate your overall mood from 1-10</ThemedText>
            
            <ThemedView style={styles.moodGrid}>
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((mood) => (
                <TouchableOpacity
                  key={mood}
                  style={[
                    styles.moodButton,
                    { backgroundColor: selectedMood === mood ? tintColor : secondaryColor }
                  ]}
                  onPress={() => setSelectedMood(mood)}
                >
                  <ThemedText style={[
                    styles.moodEmoji,
                    { color: selectedMood === mood ? 'white' : textColor }
                  ]}>
                    {moodEmojis[mood - 1]}
                  </ThemedText>
                  <ThemedText style={[
                    styles.moodNumber,
                    { color: selectedMood === mood ? 'white' : textColor }
                  ]}>
                    {mood}
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </ThemedView>
          </ThemedView>
        );

      case 2:
        return (
          <ThemedView style={styles.stepContainer}>
            <ThemedText style={styles.stepTitle}>Is this feeling pleasant or unpleasant?</ThemedText>
            <ThemedText style={styles.stepSubtitle}>Help us understand the nature of your emotion</ThemedText>
            
            <ThemedView style={styles.optionContainer}>
              <TouchableOpacity
                style={[
                  styles.optionButton,
                  { 
                    backgroundColor: pleasantness === 'pleasant' ? tintColor : cardColor,
                    borderColor: tintColor 
                  }
                ]}
                onPress={() => setPleasantness('pleasant')}
              >
                <IconSymbol 
                  size={24} 
                  name="heart.fill" 
                  color={pleasantness === 'pleasant' ? 'white' : tintColor} 
                />
                <ThemedText style={[
                  styles.optionText,
                  { color: pleasantness === 'pleasant' ? 'white' : textColor }
                ]}>
                  Pleasant
                </ThemedText>
                <ThemedText style={[
                  styles.optionDescription,
                  { color: pleasantness === 'pleasant' ? 'white' : textColor }
                ]}>
                  Positive, enjoyable feeling
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.optionButton,
                  { 
                    backgroundColor: pleasantness === 'unpleasant' ? tintColor : cardColor,
                    borderColor: tintColor 
                  }
                ]}
                onPress={() => setPleasantness('unpleasant')}
              >
                <IconSymbol 
                  size={24} 
                  name="cloud.rain.fill" 
                  color={pleasantness === 'unpleasant' ? 'white' : tintColor} 
                />
                <ThemedText style={[
                  styles.optionText,
                  { color: pleasantness === 'unpleasant' ? 'white' : textColor }
                ]}>
                  Unpleasant
                </ThemedText>
                <ThemedText style={[
                  styles.optionDescription,
                  { color: pleasantness === 'unpleasant' ? 'white' : textColor }
                ]}>
                  Difficult, challenging feeling
                </ThemedText>
              </TouchableOpacity>
            </ThemedView>
          </ThemedView>
        );

      case 3:
        return (
          <ThemedView style={styles.stepContainer}>
            <ThemedText style={styles.stepTitle}>How intense is this feeling?</ThemedText>
            <ThemedText style={styles.stepSubtitle}>Describe the strength of your emotion</ThemedText>
            
            <ThemedView style={styles.optionContainer}>
              <TouchableOpacity
                style={[
                  styles.optionButton,
                  { 
                    backgroundColor: intensity === 'mild' ? tintColor : cardColor,
                    borderColor: tintColor 
                  }
                ]}
                onPress={() => setIntensity('mild')}
              >
                <IconSymbol 
                  size={24} 
                  name="circle" 
                  color={intensity === 'mild' ? 'white' : tintColor} 
                />
                <ThemedText style={[
                  styles.optionText,
                  { color: intensity === 'mild' ? 'white' : textColor }
                ]}>
                  Mild
                </ThemedText>
                <ThemedText style={[
                  styles.optionDescription,
                  { color: intensity === 'mild' ? 'white' : textColor }
                ]}>
                  Gentle, subtle feeling
                </ThemedText>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.optionButton,
                  { 
                    backgroundColor: intensity === 'intense' ? tintColor : cardColor,
                    borderColor: tintColor 
                  }
                ]}
                onPress={() => setIntensity('intense')}
              >
                <IconSymbol 
                  size={24} 
                  name="circle.fill" 
                  color={intensity === 'intense' ? 'white' : tintColor} 
                />
                <ThemedText style={[
                  styles.optionText,
                  { color: intensity === 'intense' ? 'white' : textColor }
                ]}>
                  Intense
                </ThemedText>
                <ThemedText style={[
                  styles.optionDescription,
                  { color: intensity === 'intense' ? 'white' : textColor }
                ]}>
                  Strong, overwhelming feeling
                </ThemedText>
              </TouchableOpacity>
            </ThemedView>
          </ThemedView>
        );

      case 4:
        return (
          <ThemedView style={styles.stepContainer}>
            <ThemedText style={styles.stepTitle}>What most contributed to your current emotional state?</ThemedText>
            <ThemedText style={styles.stepSubtitle}>Think about the main trigger or cause</ThemedText>
            
            <TextInput
              style={[styles.textInput, { color: textColor, borderColor: tintColor, backgroundColor: cardColor }]}
              placeholder="e.g., Work stress, family conversation, personal achievement..."
              placeholderTextColor={textColor + '80'}
              value={mainContributor}
              onChangeText={setMainContributor}
              multiline
              numberOfLines={4}
            />
          </ThemedView>
        );

      case 5:
        return (
          <ThemedView style={styles.stepContainer}>
            <ThemedText style={styles.stepTitle}>How would you describe your thoughts or reactions to this feeling?</ThemedText>
            <ThemedText style={styles.stepSubtitle}>What's going through your mind?</ThemedText>
            
            <TextInput
              style={[styles.textInput, { color: textColor, borderColor: tintColor, backgroundColor: cardColor }]}
              placeholder="e.g., I keep thinking about what happened, I feel like I can't control this, I'm grateful for this moment..."
              placeholderTextColor={textColor + '80'}
              value={thoughtsReactions}
              onChangeText={setThoughtsReactions}
              multiline
              numberOfLines={4}
            />
          </ThemedView>
        );

      case 6:
        return (
          <ThemedView style={styles.stepContainer}>
            <ThemedText style={styles.stepTitle}>How often have you felt this way in the last month?</ThemedText>
            <ThemedText style={styles.stepSubtitle}>Help us understand patterns in your emotions</ThemedText>
            
            <ThemedView style={styles.frequencyContainer}>
              {frequencyOptions.map((option) => (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.frequencyButton,
                    { 
                      backgroundColor: frequency === option.key ? tintColor : cardColor,
                      borderColor: tintColor 
                    }
                  ]}
                  onPress={() => setFrequency(option.key as any)}
                >
                  <ThemedText style={[
                    styles.frequencyLabel,
                    { color: frequency === option.key ? 'white' : textColor }
                  ]}>
                    {option.label}
                  </ThemedText>
                  <ThemedText style={[
                    styles.frequencyDescription,
                    { color: frequency === option.key ? 'white' : textColor }
                  ]}>
                    {option.description}
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </ThemedView>
          </ThemedView>
        );

      case 7:
        return (
          <ThemedView style={styles.stepContainer}>
            <ThemedText style={styles.stepTitle}>Could you be feeling...</ThemedText>
            <ThemedText style={styles.stepSubtitle}>Select any that resonate with you</ThemedText>
            
            <ThemedView style={styles.feelingsGrid}>
              {feelingOptions.map((feeling) => (
                <TouchableOpacity
                  key={feeling}
                  style={[
                    styles.feelingButton,
                    { 
                      backgroundColor: possibleFeelings.includes(feeling) ? tintColor : secondaryColor,
                      borderColor: tintColor 
                    }
                  ]}
                  onPress={() => toggleFeeling(feeling)}
                >
                  <ThemedText style={[
                    styles.feelingText,
                    { color: possibleFeelings.includes(feeling) ? 'white' : textColor }
                  ]}>
                    {feeling}
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </ThemedView>

            <ThemedText style={styles.stepTitle}>Why are you feeling this way?</ThemedText>
            <TextInput
              style={[styles.textInput, { color: textColor, borderColor: tintColor, backgroundColor: cardColor }]}
              placeholder="Reflect on the deeper reasons behind your emotions..."
              placeholderTextColor={textColor + '80'}
              value={reasonsWhy}
              onChangeText={setReasonsWhy}
              multiline
              numberOfLines={3}
            />
          </ThemedView>
        );

      default:
        return null;
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor }]}>
      <ThemedView style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: cardColor, borderColor: tintColor }]}
          onPress={() => router.back()}
        >
          <IconSymbol size={20} name="chevron.left" color={tintColor} />
        </TouchableOpacity>
        <ThemedView style={styles.headerText}>
          <ThemedText type="title">Mood Assessment</ThemedText>
          <ThemedText style={styles.subtitle}>Step {currentStep} of 7</ThemedText>
        </ThemedView>
      </ThemedView>

      {/* Progress Bar */}
      <ThemedView style={styles.progressContainer}>
        <ThemedView style={[styles.progressBar, { backgroundColor: secondaryColor }]}>
          <ThemedView 
            style={[
              styles.progressFill, 
              { 
                backgroundColor: tintColor,
                width: `${(currentStep / 7) * 100}%` 
              }
            ]} 
          />
        </ThemedView>
      </ThemedView>

      {renderStep()}

      {/* Navigation Buttons */}
      <ThemedView style={styles.navigationContainer}>
        {currentStep > 1 && (
          <TouchableOpacity
            style={[styles.navButton, styles.prevButton, { borderColor: tintColor }]}
            onPress={prevStep}
          >
            <ThemedText style={[styles.navButtonText, { color: tintColor }]}>Previous</ThemedText>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[
            styles.navButton, 
            styles.nextButton,
            { 
              backgroundColor: canProceed() ? tintColor : secondaryColor,
              opacity: canProceed() ? 1 : 0.5 
            }
          ]}
          onPress={nextStep}
          disabled={!canProceed()}
        >
          <ThemedText style={styles.nextButtonText}>
            {currentStep === 7 ? 'Complete' : 'Next'}
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: 'transparent',
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  headerText: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
    marginTop: 4,
  },
  progressContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  stepContainer: {
    padding: 20,
    backgroundColor: 'transparent',
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  stepSubtitle: {
    fontSize: 16,
    opacity: 0.7,
    marginBottom: 24,
    textAlign: 'center',
  },
  moodGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    backgroundColor: 'transparent',
  },
  moodButton: {
    width: '18%',
    aspectRatio: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    marginBottom: 8,
  },
  moodEmoji: {
    fontSize: 20,
  },
  moodNumber: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
  },
  optionContainer: {
    gap: 16,
  },
  optionButton: {
    padding: 20,
    borderRadius: 16,
    borderWidth: 2,
    alignItems: 'center',
  },
  optionText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 8,
  },
  optionDescription: {
    fontSize: 14,
    opacity: 0.8,
    marginTop: 4,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    textAlignVertical: 'top',
    minHeight: 100,
  },
  frequencyContainer: {
    gap: 12,
  },
  frequencyButton: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  frequencyLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  frequencyDescription: {
    fontSize: 14,
    opacity: 0.8,
    marginTop: 4,
  },
  feelingsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 24,
    backgroundColor: 'transparent',
  },
  feelingButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  feelingText: {
    fontSize: 14,
    fontWeight: '500',
  },
  navigationContainer: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
    backgroundColor: 'transparent',
  },
  navButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  prevButton: {
    borderWidth: 1,
  },
  nextButton: {
    // backgroundColor set dynamically
  },
  navButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  nextButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});