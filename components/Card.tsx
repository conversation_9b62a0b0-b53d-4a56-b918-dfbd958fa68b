import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { useThemeColor } from '@/hooks/useThemeColor';

export type CardProps = {
  children: React.ReactNode;
  style?: ViewStyle;
  padding?: number;
  margin?: number;
};

export function Card({ children, style, padding = 20, margin = 0 }: CardProps) {
  const cardColor = useThemeColor({}, 'card');
  const borderColor = useThemeColor({}, 'border');

  const cardStyle: ViewStyle = {
    backgroundColor: cardColor,
    borderRadius: 15,
    padding: padding,
    margin: margin,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08, // Slightly softer shadow for the Tranquil Growth theme
    shadowRadius: 3.84,
    elevation: 4, // Slightly reduced elevation for a softer appearance
    borderWidth: 1,
    borderColor: borderColor,
  };

  return <View style={[cardStyle, style]}>{children}</View>;
}