import React from 'react';
import { TouchableOpacity, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { ThemedText } from './ThemedText';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Colors } from '@/constants/Colors';

// Tranquil Growth palette constants for direct access
const backgroundColorLight = '#FAF9F6'; // Warm Off-White

export type ButtonProps = {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  icon?: React.ReactNode;
};

export function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  style,
  textStyle,
  icon,
}: ButtonProps) {
  const tintColor = useThemeColor({}, 'tint');
  const accentColor = useThemeColor({}, 'accent');
  const textColor = useThemeColor({}, 'text');
  const backgroundColor = useThemeColor({}, 'background');

  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 25, // Pill-shaped buttons
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    };

    // Size variations
    const sizeStyles = {
      small: { paddingVertical: 8, paddingHorizontal: 16 },
      medium: { paddingVertical: 12, paddingHorizontal: 24 },
      large: { paddingVertical: 16, paddingHorizontal: 32 },
    };

    // Variant styles - Updated for Tranquil Growth palette
    const variantStyles = {
      primary: {
        backgroundColor: disabled ? Colors.light.muted : accentColor, // Soft Coral/Terracotta for primary actions
      },
      secondary: {
        backgroundColor: disabled ? Colors.light.muted : tintColor, // Muted Sage Green for secondary actions
      },
      outline: {
        backgroundColor: 'transparent',
        borderWidth: 2,
        borderColor: disabled ? Colors.light.muted : tintColor, // Muted Sage Green border
        shadowOpacity: 0, // No shadow for outline buttons
        elevation: 0,
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  const getTextColor = (): string => {
    if (disabled) return Colors.light.muted;
    if (variant === 'outline') return tintColor; // Muted Sage Green for outline button text
    return backgroundColorLight; // Warm Off-White text for filled buttons
  };

  const getTextSize = (): 'body' | 'defaultSemiBold' | 'subtitle' => {
    switch (size) {
      case 'small': return 'body';
      case 'large': return 'subtitle';
      default: return 'defaultSemiBold';
    }
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      {icon && <>{icon}</>}
      <ThemedText
        type={getTextSize()}
        style={[
          { color: getTextColor(), marginLeft: icon ? 8 : 0 },
          textStyle,
        ]}
      >
        {title}
      </ThemedText>
    </TouchableOpacity>
  );
}