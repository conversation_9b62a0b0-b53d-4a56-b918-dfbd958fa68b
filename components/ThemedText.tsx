import { StyleSheet, Text, type TextProps, PixelRatio } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?: 'default' | 'title' | 'defaultSemiBold' | 'subtitle' | 'link' | 'caption' | 'body' | 'heading';
};

// Dynamic font scaling based on device settings
const getFontSize = (size: number) => {
  const scale = PixelRatio.getFontScale();
  return size * Math.min(scale, 1.3); // Cap scaling at 1.3x for readability
};

export function ThemedText({
  style,
  lightColor,
  darkColor,
  type = 'default',
  ...rest
}: ThemedTextProps) {
  const color = useThemeColor({ light: lightColor, dark: darkColor }, type === 'link' ? 'tint' : 'text');

  return (
    <Text
      style={[
        { color },
        type === 'default' ? styles.default : undefined,
        type === 'title' ? styles.title : undefined,
        type === 'defaultSemiBold' ? styles.defaultSemiBold : undefined,
        type === 'subtitle' ? styles.subtitle : undefined,
        type === 'link' ? styles.link : undefined,
        type === 'caption' ? styles.caption : undefined,
        type === 'body' ? styles.body : undefined,
        type === 'heading' ? styles.heading : undefined,
        style,
      ]}
      {...rest}
    />
  );
}

const styles = StyleSheet.create({
  default: {
    fontSize: getFontSize(16),
    lineHeight: getFontSize(24), // 1.5 line height for better readability
    fontFamily: 'Inter_400Regular',
  },
  body: {
    fontSize: getFontSize(16),
    lineHeight: getFontSize(24), // 1.5 line height
    fontFamily: 'Inter_400Regular',
  },
  defaultSemiBold: {
    fontSize: getFontSize(16),
    lineHeight: getFontSize(24),
    fontFamily: 'Inter_600SemiBold',
    fontWeight: '600',
  },
  title: {
    fontSize: getFontSize(32),
    fontFamily: 'SpaceMono', // Keep SpaceMono for titles
    fontWeight: 'bold',
    lineHeight: getFontSize(40), // Better spacing for titles
  },
  heading: {
    fontSize: getFontSize(24),
    fontFamily: 'Inter_700Bold',
    fontWeight: 'bold',
    lineHeight: getFontSize(32),
  },
  subtitle: {
    fontSize: getFontSize(20),
    fontFamily: 'Inter_600SemiBold',
    fontWeight: '600',
    lineHeight: getFontSize(28),
  },
  caption: {
    fontSize: getFontSize(12),
    fontFamily: 'Inter_400Regular',
    lineHeight: getFontSize(16),
  },
  link: {
    lineHeight: getFontSize(24),
    fontSize: getFontSize(16),
    fontFamily: 'Inter_500Medium',
    fontWeight: '500',
  },
});
